// Test script to verify schedule-task sync functionality
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://qxgfulribhhohmggyroq.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzAwODEyOSwiZXhwIjoyMDY4NTg0MTI5fQ.Uw77zE5XbMWyNnFpTeh_neOb3xmKLss8lbDgkjw3zoU'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testScheduleTaskSync() {
  try {
    console.log('🧪 Testing schedule-task sync functionality...')
    
    // 1. Find a schedule with tasks
    const { data: schedules, error: schedulesError } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        scheduled_date,
        project_id,
        projects!inner (
          name,
          clients!inner (
            client_type
          )
        )
      `)
      .limit(5)
    
    if (schedulesError) {
      throw new Error(`Failed to fetch schedules: ${schedulesError.message}`)
    }
    
    if (!schedules || schedules.length === 0) {
      console.log('❌ No schedules found')
      return
    }
    
    console.log(`📋 Found ${schedules.length} schedules`)
    
    // Find a schedule with tasks
    let testSchedule = null
    for (const schedule of schedules) {
      const { data: tasks } = await supabase
        .from('tasks')
        .select('id, title, due_date')
        .eq('shoot_id', schedule.id)
      
      if (tasks && tasks.length > 0) {
        testSchedule = schedule
        console.log(`✅ Using schedule ${schedule.custom_id} with ${tasks.length} tasks`)
        console.log('Current tasks:', tasks.map(t => ({ title: t.title, due_date: t.due_date })))
        break
      }
    }
    
    if (!testSchedule) {
      console.log('❌ No schedule with tasks found')
      return
    }
    
    // 2. Get current task due dates
    const { data: beforeTasks } = await supabase
      .from('tasks')
      .select('id, title, due_date')
      .eq('shoot_id', testSchedule.id)
    
    console.log('\n📅 Current task due dates:')
    beforeTasks?.forEach(task => {
      console.log(`  - ${task.title}: ${task.due_date}`)
    })
    
    // 3. Update the schedule date (move it 2 days forward)
    const currentDate = new Date(testSchedule.scheduled_date)
    const newDate = new Date(currentDate)
    newDate.setDate(newDate.getDate() + 2)
    const newScheduledDate = newDate.toISOString()
    
    console.log(`\n🔄 Updating schedule date:`)
    console.log(`  From: ${testSchedule.scheduled_date}`)
    console.log(`  To: ${newScheduledDate}`)
    
    // Call the API endpoint to update the schedule (this should trigger task sync)
    const response = await fetch(`http://localhost:3000/api/schedules/${testSchedule.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        scheduled_date: newScheduledDate
      }),
    })
    
    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`API call failed: ${errorData.error}`)
    }
    
    const updatedSchedule = await response.json()
    console.log('✅ Schedule updated successfully')
    
    // 4. Check if task due dates were updated
    await new Promise(resolve => setTimeout(resolve, 1000)) // Wait a bit for sync
    
    const { data: afterTasks } = await supabase
      .from('tasks')
      .select('id, title, due_date')
      .eq('shoot_id', testSchedule.id)
    
    console.log('\n📅 Updated task due dates:')
    afterTasks?.forEach(task => {
      const beforeTask = beforeTasks?.find(t => t.id === task.id)
      const changed = beforeTask?.due_date !== task.due_date
      console.log(`  - ${task.title}: ${task.due_date} ${changed ? '✅ CHANGED' : '⚠️ UNCHANGED'}`)
    })
    
    // 5. Revert the schedule date back to original
    console.log('\n🔄 Reverting schedule date back to original...')
    const revertResponse = await fetch(`http://localhost:3000/api/schedules/${testSchedule.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        scheduled_date: testSchedule.scheduled_date
      }),
    })
    
    if (revertResponse.ok) {
      console.log('✅ Schedule reverted successfully')
    } else {
      console.log('⚠️ Failed to revert schedule')
    }
    
    console.log('\n🎉 Test completed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testScheduleTaskSync()
