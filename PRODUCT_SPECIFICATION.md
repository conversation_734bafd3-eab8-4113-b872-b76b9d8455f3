# Product Specification: Cymatics Project Management System

## 1. Introduction

**Product Name:** Cymatics

**Vision:** To create a centralized, efficient, and intuitive platform for managing the entire lifecycle of production projects, from initial scheduling to final vendor payments. Cymatics aims to streamline operations, improve collaboration, and provide clear visibility into project status and financials.

**Overview:** Cymatics is a web-based application designed to manage production schedules (formerly "shoots"), vendor data, project financials, and associated documentation. It integrates with SharePoint for robust document management and provides role-based access for different user types (e.g., admins, project managers).

## 2. Goals and Objectives

- **Centralize Project Data:** Consolidate all information related to schedules, vendors, clients, and payments into a single source of truth.
- **Streamline Scheduling:** Simplify the process of creating, viewing, and managing complex production schedules.
- **Automate Financial Workflows:** Automate calculations for vendor payments, track expenses, and ensure timely and accurate financial processing.
- **Enhance Collaboration:** Provide a shared platform for team members to access and update project information.
- **Improve Reporting & Visibility:** Offer clear dashboards and reports on project progress, financial health, and resource allocation.
- **Integrate Seamlessly:** Connect with essential third-party services like SharePoint for document management.

## 3. Target Audience

- **Project Managers:** Responsible for planning, executing, and closing projects. They are the primary users for creating schedules and managing day-to-day operations.
- **Finance/Accounting Team:** Responsible for processing vendor payments, tracking expenses, and managing budgets.
- **Company Administrators:** Responsible for user management, system configuration, and overall platform oversight.
- **Vendors (External):** May have limited access to view their schedules, payment status, and submit necessary documents.

## 4. Features and Functionality

### 4.1. Dashboard
-   **Overview:** A personalized landing page displaying key information like upcoming schedules, pending tasks, recent activity, and high-level financial summaries.

### 4.2. Schedule Management
-   **Create/Edit Schedules:** Form to create new schedules with details such as project name, client, date, time, location, and required personnel/vendors.
-   **Calendar View:** A visual calendar (day, week, month) displaying all schedules.
-   **List View:** A searchable and filterable table of all schedules.
-   **Status Tracking:** Update the status of schedules (e.g., Planned, Confirmed, In Progress, Completed, Canceled).

### 4.3. Vendor Management
-   **Vendor Database:** A central repository for all vendor information (contact details, payment info, service type, etc.).
-   **Vendor Onboarding:** A process for adding and vetting new vendors.
-   **Payment Tracking:** Track payments owed, processed, and pending for each vendor across multiple projects.
-   **Outsourcing Management:** Special handling for outsourced vendors, including custom ID and data tracking.

### 4.4. Project & Client Management
-   **Project Hub:** A dedicated page for each project, consolidating all related schedules, vendors, expenses, and documents.
-   **Client Database:** Manage client information, including contact persons and project history.

### 4.5. Financial Management
-   **Expense Tracking:** Log and categorize project-related expenses.
-   **Payment Calculation Engine:** Automatically calculate vendor payments based on schedules, rates, and other variables.
-   **Payment Verification:** A workflow for reviewing and approving final payments.

### 4.6. Document Management (SharePoint Integration)
-   **Automated Folder Creation:** Automatically create dedicated SharePoint folders for new projects and schedules.
-   **Document Linking:** Link relevant documents from SharePoint directly to schedules and projects within the app.
-   **Secure Access:** Leverage SharePoint's permissions model for secure document access.

### 4.7. User Management & Authentication
-   **Role-Based Access Control (RBAC):** Define roles (e.g., Admin, Manager, Viewer) with specific permissions.
-   **User Profiles:** Manage user accounts and roles.
-   **Secure Login:** Email/password-based authentication.

## 5. Technical Requirements

-   **Frontend:** Next.js, React, TypeScript, Tailwind CSS
-   **Backend:** Node.js (via Next.js API routes), Supabase Edge Functions
-   **Database:** Supabase (PostgreSQL)
-   **Authentication:** Supabase Auth
-   **External Integrations:** Microsoft Graph API for SharePoint
-   **Deployment:** Vercel

## 6. Design & UX Requirements

-   **UI:** Clean, modern, and intuitive interface. Adhere to a consistent design system.
-   **Responsiveness:** The application must be fully responsive and usable on desktop, tablet, and mobile devices.
-   **Accessibility:** Follow WCAG 2.1 AA guidelines to ensure the application is usable by people with disabilities.

## 7. Non-Functional Requirements

-   **Performance:** Pages should load quickly, and database queries must be optimized. API responses should be under 200ms for typical requests.
-   **Scalability:** The system should be able to handle a growing number of users, projects, and data without degradation in performance.
-   **Security:** All data transmission must be over HTTPS. Implement strong security practices to prevent common vulnerabilities (e.g., SQL injection, XSS). Sensitive data in the database should be encrypted.
-   **Reliability:** The application should have high uptime (e.g., 99.9%) and be resilient to errors.

## 8. Assumptions and Constraints

-   **Assumption:** Users will have a modern web browser (e.g., Chrome, Firefox, Safari, Edge).
-   **Assumption:** A stable internet connection is required for application use.
-   **Constraint:** The primary integration for document management will be Microsoft SharePoint. Other cloud storage providers are out of scope for the initial version.

## 9. Future Scope / Roadmap

-   **Advanced Reporting:** Customizable reports and data visualization.
-   **Client Portal:** A dedicated portal for clients to view project progress and approve items.
-   **Mobile App:** Native mobile applications (iOS/Android) for on-the-go access.
-   **Additional Integrations:** Integration with accounting software (e.g., QuickBooks, Xero).
-   **Notifications:** In-app and email notifications for important events (e.g., schedule changes, payment approvals).
