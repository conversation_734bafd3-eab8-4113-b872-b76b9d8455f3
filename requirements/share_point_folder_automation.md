# SharePoint Folder Automation via Graph API

## Objective

Automate folder creation in SharePoint using the Microsoft Graph API whenever new entities are created in the Cymatics system, including clients, projects, and schedules. The folder naming and structure must follow Cymatics' custom ID conventions and hierarchy.

---

## Entity Types & Folder Naming Rules

### 1. Clients

* **Format:** `{ClientCustomID} {ClientName}`
* **Example:** `CYMCL-25-001 Cybermedia`
* **Location:** Top-level folder in the SharePoint document library.

### 2. Projects

* **Format:** `{ProjectCustomID} {ProjectName}`
* **Example:** `CYMPR-25-002 Aerial Mapping`
* **Location:** Inside the parent client’s folder.

### 3. Schedules

* **Format:** `{ScheduleCustomID} {YYYY-MM-DD}`
* **Example:** `CYM-25-005 2025-08-09`
* **Location:** Inside the parent project’s folder.
* **Subfolders:** Each schedule folder must contain two subfolders:

  * `Raw`
  * `Output`

## 🔧 Build a Microsoft Graph API integration

### Environment Variables

* `MICROSOFT_CLIENT_ID`
* `MICROSOFT_CLIENT_SECRET`
* `MICROSOFT_TENANT_ID`

### 🎯 App Logic

1. Authenticate to Microsoft Graph using the **Client Credentials flow**.
2. Access SharePoint document library:

   * Site URL: `https://zn6bn.sharepoint.com/sites/files`
   * Drive name: `Cymatics files`
   * Drive ID: `b!6BliBHB7rkqGeijn571F56igGQK5UQ9GkI_AIFbTSvvooGrFp94hQr6gPCGK1yYV`
3. On **Client creation** → Create `{ClientCustomID} {ClientName}` folder in root.
4. On **Project creation** → Create `{ProjectCustomID} {ProjectName}` inside respective client folder.
5. On **Schedule creation** → Create `{ScheduleCustomID} {YYYY-MM-DD}` inside respective project folder, then create `Raw` and `Output` subfolders inside the schedule folder.
6. Return SharePoint folder URL: `https://zn6bn.sharepoint.com/sites/files/Shared%20Documents/<folder-name>`.

---

## Technical Notes

* Use Microsoft Graph v1.0.
* Use POST `/drives/{driveId}/root/children` for folder creation.
* Handle token caching or re-authentication.
* Use only `.env` variables for secrets (no hardcoding).
* Skip folder creation if it already exists.
* Maintain entity-to-folder path mapping in DB.

**Example Folder Creation Request:**

```json
POST https://graph.microsoft.com/v1.0/drives/{drive-id}/root:/path:/children
{
  "name": "CYMCL-25-001 Cybermedia",
  "folder": {},
  "@microsoft.graph.conflictBehavior": "fail"
}
```

---

## Testing

* Validate creation logic for all entity types.
* Check correct nesting for clients → projects → schedules → subfolders.
* Test year rollover ID formatting.
