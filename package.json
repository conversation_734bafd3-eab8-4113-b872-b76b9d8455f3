{"name": "cymatics", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.10", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/mcp-server-supabase": "^0.4.5", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dexie": "^4.0.11", "dotenv": "^17.2.1", "exifr": "^7.1.3", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "next": "15.4.2", "piexifjs": "^1.0.6", "react": "19.1.0", "react-big-calendar": "^1.19.4", "react-day-picker": "^9.8.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "srt-parser-2": "^1.2.3", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.5", "xlsx": "^0.18.5", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^3.4.13", "typescript": "^5"}}