## **Cymatics Heatmap Automation – Requirements & To-Do**

### **Objective**

Automate extraction and organization of geolocation metadata from drone files (photos, videos, SRT) in SharePoint, and generate per-folder `geo_metadata.json` for direct rendering in Cymatics app heatmap view.

---

### **File Handling Rules**

1. **File Types**

   * Photos: `.jpg`, `.jpeg`, `.png` (EXIF geotagged)
   * Videos: `.mp4`, `.mov` (GPS from SRT file)
   * Subtitles: `.srt` (video geolocation & timestamps)
   * Other drone data: `.lrf` (ignored for heatmap)

2. **Auto-Organize by Type** in each schedule folder:

   * `/Raw/Photos`
   * `/Raw/Videos`
   * `/Raw/SRT`
   * `/Raw/LRF`

3. **Duplicate Handling**

   * If same filename exists, auto-rename with suffix `(_1)`, `(_2)`, etc.
   * Never overwrite or delete files.

---

### **Process Flow**

Triggered **when file upload task is marked as completed** in Cymatics app.

1. **List Files in Schedule Folder**

   * Use Microsoft Graph API `/drives/{driveId}/items/{itemId}/children`.

2. **Move Files into Type Folders**

   * Create subfolders if not present.
   * Move each file into appropriate folder.

3. **Extract Metadata**

   * **Photos** → Read EXIF GPS directly from SharePoint file stream (no full download).
   * **Videos** → Locate matching `.srt` in SRT folder, parse first & last GPS points.
   * Ignore non-geotagged files.

4. \*\*Generate \*\***`geo_metadata.json`**

   * Save in root of schedule folder.
   * Format:

     ```json
     [
       {"type": "photo", "filename": "IMG_001.jpg", "lat": xx.xxxxx, "lng": yy.yyyyy, "timestamp": "2025-08-12T14:00:00Z"},
       {"type": "video", "filename": "VID_001.mp4", "start": {"lat": xx.xxxx, "lng": yy.yyyy}, "end": {"lat": xx.xxxx, "lng": yy.yyyy}}
     ]
     ```

5. **Share Public Link**

   * Create “anyone with link” sharing URL for the `geo_metadata.json` file.
   * Store link in Cymatics database for map rendering.

---

### **Technical Requirements**

* **APIs:** Microsoft Graph v1.0
* **Environment Variables:**

  * `MICROSOFT_CLIENT_ID`
  * `MICROSOFT_CLIENT_SECRET`
  * `MICROSOFT_TENANT_ID`
* **Libraries for EXIF & SRT parsing:**

  * `exiftool` or `piexif` (Python/Node)
  * `srt` (Node/Python SRT parser)
* **Authentication:** Client Credentials Flow
* **Performance Goal:** <20 seconds for 100 files (parallel processing)

---

### **To-Do**

* Implement Graph API authentication & token caching
* Auto-create file type folders (`Raw/Photos`, etc.)
* Implement duplicate file renaming logic
* Implement in-memory EXIF & SRT parsing
* Generate & save `geo_metadata.json`
* Create public sharing link for JSON
* Test with large folder (100+ files)
* Integrate with Cymatics app’s heatmap viewer in map page.

---
