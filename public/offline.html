<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Cymatics</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .features {
            text-align: left;
            margin: 30px 0;
        }
        
        .features h3 {
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .features ul {
            list-style: none;
            padding-left: 0;
        }
        
        .features li {
            padding: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4ade80;
            font-weight: bold;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .status {
            margin-top: 30px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            font-size: 0.9rem;
        }
        
        .online {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        
        .offline {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        
        @media (max-width: 600px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .icon {
                width: 60px;
                height: 60px;
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📡</div>
        <h1>You're Offline</h1>
        <p>Don't worry! Cymatics works offline too. You can still access your cached data and continue working.</p>
        
        <div class="features">
            <h3>Available Offline:</h3>
            <ul>
                <li>View cached projects and tasks</li>
                <li>Access recent dashboard data</li>
                <li>Browse client information</li>
                <li>Review schedule details</li>
            </ul>
        </div>
        
        <div class="status offline" id="status">
            <strong>Status:</strong> <span id="statusText">Offline</span>
        </div>
        
        <a href="/" class="retry-btn" id="retryBtn">Try Again</a>
    </div>
    
    <script>
        // Check online status
        function updateStatus() {
            const status = document.getElementById('status');
            const statusText = document.getElementById('statusText');
            const retryBtn = document.getElementById('retryBtn');
            
            if (navigator.onLine) {
                status.className = 'status online';
                statusText.textContent = 'Back Online!';
                retryBtn.textContent = 'Return to App';
                retryBtn.style.background = 'rgba(34, 197, 94, 0.3)';
                retryBtn.style.borderColor = 'rgba(34, 197, 94, 0.5)';
            } else {
                status.className = 'status offline';
                statusText.textContent = 'Offline';
                retryBtn.textContent = 'Try Again';
                retryBtn.style.background = 'rgba(255, 255, 255, 0.2)';
                retryBtn.style.borderColor = 'rgba(255, 255, 255, 0.3)';
            }
        }
        
        // Listen for online/offline events
        window.addEventListener('online', updateStatus);
        window.addEventListener('offline', updateStatus);
        
        // Initial status check
        updateStatus();
        
        // Auto-retry when back online
        window.addEventListener('online', () => {
            setTimeout(() => {
                window.location.href = '/';
            }, 2000);
        });
        
        // Retry button functionality
        document.getElementById('retryBtn').addEventListener('click', (e) => {
            if (!navigator.onLine) {
                e.preventDefault();
                // Show a message that they're still offline
                const btn = e.target;
                const originalText = btn.textContent;
                btn.textContent = 'Still Offline...';
                setTimeout(() => {
                    btn.textContent = originalText;
                }, 2000);
            }
        });
    </script>
</body>
</html>
