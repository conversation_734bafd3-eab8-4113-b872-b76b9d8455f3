// Service Worker for caching and performance optimization
const CACHE_NAME = 'cymatics-v1'
const STATIC_CACHE_NAME = 'cymatics-static-v1'
const API_CACHE_NAME = 'cymatics-api-v1'

// Static assets to cache
const STATIC_ASSETS = [
  '/',
  '/dashboard',
  '/projects',
  '/tasks',
  '/schedules',
  '/clients',
  '/manifest.json',
  '/offline.html'
]

// API endpoints to cache (with short TTL)
const API_ENDPOINTS = [
  '/api/dashboard/stats',
  '/api/projects',
  '/api/clients',
  '/api/users'
]

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...')
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        console.log('Caching static assets')
        return cache.addAll(STATIC_ASSETS)
      }),
      caches.open(API_CACHE_NAME).then((cache) => {
        console.log('API cache initialized')
        return Promise.resolve()
      })
    ])
  )
  self.skipWaiting()
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...')
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== STATIC_CACHE_NAME && cacheName !== API_CACHE_NAME) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
  self.clients.claim()
})

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }

  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return
  }

  // API requests - Network First with cache fallback
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(
      networkFirstStrategy(request, API_CACHE_NAME, 5 * 60 * 1000) // 5 minutes TTL
    )
    return
  }

  // Static assets - Cache First
  if (STATIC_ASSETS.some(asset => url.pathname === asset || url.pathname.startsWith(asset))) {
    event.respondWith(
      cacheFirstStrategy(request, STATIC_CACHE_NAME)
    )
    return
  }

  // Other requests - Network First
  event.respondWith(
    networkFirstStrategy(request, STATIC_CACHE_NAME)
  )
})

// Cache First Strategy - for static assets
async function cacheFirstStrategy(request, cacheName) {
  try {
    const cache = await caches.open(cacheName)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      console.log('Cache hit:', request.url)
      return cachedResponse
    }

    console.log('Cache miss, fetching:', request.url)
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    console.error('Cache first strategy failed:', error)
    return new Response('Network error', { status: 408 })
  }
}

// Network First Strategy - for API calls and dynamic content
async function networkFirstStrategy(request, cacheName, ttl = 0) {
  try {
    const cache = await caches.open(cacheName)
    
    try {
      console.log('Network first, fetching:', request.url)
      const networkResponse = await fetch(request)
      
      if (networkResponse.ok) {
        // Add timestamp for TTL checking
        const responseToCache = networkResponse.clone()
        if (ttl > 0) {
          const headers = new Headers(responseToCache.headers)
          headers.set('sw-cache-timestamp', Date.now().toString())
          const modifiedResponse = new Response(responseToCache.body, {
            status: responseToCache.status,
            statusText: responseToCache.statusText,
            headers: headers
          })
          cache.put(request, modifiedResponse)
        } else {
          cache.put(request, responseToCache)
        }
      }
      
      return networkResponse
    } catch (networkError) {
      console.log('Network failed, trying cache:', request.url)
      const cachedResponse = await cache.match(request)
      
      if (cachedResponse) {
        // Check TTL if applicable
        if (ttl > 0) {
          const cacheTimestamp = cachedResponse.headers.get('sw-cache-timestamp')
          if (cacheTimestamp) {
            const age = Date.now() - parseInt(cacheTimestamp)
            if (age > ttl) {
              console.log('Cached response expired:', request.url)
              throw new Error('Cache expired')
            }
          }
        }
        
        console.log('Serving from cache:', request.url)
        return cachedResponse
      }
      
      throw networkError
    }
  } catch (error) {
    console.error('Network first strategy failed:', error)
    return new Response('Service unavailable', { status: 503 })
  }
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Background sync triggered:', event.tag)
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Handle background sync tasks
      handleBackgroundSync()
    )
  }
})

async function handleBackgroundSync() {
  try {
    // Process any queued offline actions
    console.log('Processing background sync tasks...')
    // Implementation would depend on your offline strategy
  } catch (error) {
    console.error('Background sync failed:', error)
  }
}

// Push notification handling (for future use)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json()
    console.log('Push notification received:', data)
    
    event.waitUntil(
      self.registration.showNotification(data.title, {
        body: data.body,
        icon: '/icon-192x192.png',
        badge: '/icon-72x72.png',
        tag: data.tag || 'default',
        data: data.data
      })
    )
  }
})

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event.notification.data)
  event.notification.close()
  
  event.waitUntil(
    clients.openWindow(event.notification.data?.url || '/')
  )
})

console.log('Service Worker loaded successfully')
