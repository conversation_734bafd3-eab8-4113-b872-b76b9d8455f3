<!DOCTYPE html>
<html>
<head>
    <title>PWA Icon Generator</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .icon-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; }
        .icon-item { text-align: center; }
    </style>
</head>
<body>
    <h1>Cymatics PWA Icon Generator</h1>
    <p>This page generates placeholder icons for the PWA. Replace with actual branded icons.</p>
    
    <div class="icon-grid" id="iconGrid"></div>
    
    <script>
        const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
        const iconGrid = document.getElementById('iconGrid');
        
        sizes.forEach(size => {
            const container = document.createElement('div');
            container.className = 'icon-item';
            
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            
            const ctx = canvas.getContext('2d');
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#3b82f6');
            gradient.addColorStop(1, '#1d4ed8');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Add rounded corners
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.2);
            ctx.fill();
            
            // Reset composite operation
            ctx.globalCompositeOperation = 'source-over';
            
            // Add text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.15}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('C', size / 2, size / 2);
            
            // Add smaller text
            ctx.font = `${size * 0.08}px Arial`;
            ctx.fillText('Cymatics', size / 2, size * 0.75);
            
            const label = document.createElement('p');
            label.textContent = `${size}x${size}`;
            
            const downloadLink = document.createElement('a');
            downloadLink.href = canvas.toDataURL();
            downloadLink.download = `icon-${size}x${size}.png`;
            downloadLink.textContent = 'Download';
            downloadLink.style.display = 'block';
            downloadLink.style.marginTop = '10px';
            
            container.appendChild(canvas);
            container.appendChild(label);
            container.appendChild(downloadLink);
            iconGrid.appendChild(container);
        });
        
        // Auto-download all icons
        setTimeout(() => {
            const links = document.querySelectorAll('a[download]');
            links.forEach((link, index) => {
                setTimeout(() => link.click(), index * 100);
            });
        }, 1000);
    </script>
</body>
</html>
