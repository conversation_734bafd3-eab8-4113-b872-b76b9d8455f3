'use client'

import { useState } from 'react'
import { projects<PERSON><PERSON>, users<PERSON>pi } from '@/lib/api'
import { getDefaultTasksForClientType, getClientTypesWithDefaultTasks, DEFAULT_TASK_TEMPLATES } from '@/lib/default-tasks'
import { CLIENT_TYPES, type ClientType } from '@/lib/constants'

export default function DebugDefaultTasksPage() {
  const [results, setResults] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [testProjectId, setTestProjectId] = useState('01234567-89ab-cdef-0123-456789abcdef')
  const [testClientType, setTestClientType] = useState<ClientType>('Wedding')

  const log = (message: string) => {
    console.log(message)
    setResults(prev => prev + message + '\n')
  }

  const clearResults = () => {
    setResults('')
    console.clear()
  }

  const runDiagnostics = async () => {
    setIsLoading(true)
    clearResults()
    
    try {
      log('🔍 COMPREHENSIVE DEFAULT TASK CREATION DIAGNOSTICS')
      log('=' .repeat(60))
      
      // 1. Check client type consistency
      log('\n📋 1. CLIENT TYPE CONSISTENCY CHECK')
      log('-'.repeat(40))
      
      const constantsClientTypes = CLIENT_TYPES
      const templateClientTypes = Object.keys(DEFAULT_TASK_TEMPLATES)
      const availableClientTypes = getClientTypesWithDefaultTasks()
      
      log(`Constants CLIENT_TYPES: [${constantsClientTypes.join(', ')}]`)
      log(`Template keys: [${templateClientTypes.join(', ')}]`)
      log(`Available client types: [${availableClientTypes.join(', ')}]`)
      
      // Find mismatches
      const missingInTemplates = constantsClientTypes.filter(type => !templateClientTypes.includes(type))
      const extraInTemplates = templateClientTypes.filter(type => !constantsClientTypes.includes(type))
      
      if (missingInTemplates.length > 0) {
        log(`❌ Missing in templates: [${missingInTemplates.join(', ')}]`)
      }
      if (extraInTemplates.length > 0) {
        log(`⚠️  Extra in templates: [${extraInTemplates.join(', ')}]`)
      }
      if (missingInTemplates.length === 0 && extraInTemplates.length === 0) {
        log('✅ Client types are consistent')
      }
      
      // 2. Check user roles
      log('\n👥 2. USER ROLES CHECK')
      log('-'.repeat(40))
      
      try {
        const usersByRole = await usersApi.getUsersByRoles()
        log(`Pilot users: ${usersByRole.pilot.length} (${usersByRole.pilot.map(u => u.email).join(', ')})`)
        log(`Editor users: ${usersByRole.editor.length} (${usersByRole.editor.map(u => u.email).join(', ')})`)
        log(`Accounts users: ${usersByRole.accounts.length} (${usersByRole.accounts.map(u => u.email).join(', ')})`)
        
        if (usersByRole.pilot.length === 0 && usersByRole.editor.length === 0 && usersByRole.accounts.length === 0) {
          log('❌ No users with required roles found!')
        } else {
          log('✅ Users with required roles found')
        }
      } catch (error) {
        log(`❌ Error fetching users: ${error}`)
      }
      
      // 3. Check template retrieval for test client type
      log(`\n📝 3. TEMPLATE RETRIEVAL TEST (${testClientType})`)
      log('-'.repeat(40))
      
      try {
        const templates = getDefaultTasksForClientType(testClientType)
        log(`Templates found: ${templates.length}`)
        
        if (templates.length > 0) {
          log('Template details:')
          templates.forEach((template, index) => {
            log(`  ${index + 1}. ${template.title} (${template.assigned_role}, ${template.isProjectTask ? 'project' : 'shoot'} task)`)
          })
        } else {
          log(`❌ No templates found for client type: ${testClientType}`)
        }
      } catch (error) {
        log(`❌ Error getting templates: ${error}`)
      }
      
      // 4. Test UUID validation
      log('\n🔍 4. UUID VALIDATION TEST')
      log('-'.repeat(40))
      
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
      const isValidUuid = uuidRegex.test(testProjectId)
      log(`Test Project ID: ${testProjectId}`)
      log(`UUID validation: ${isValidUuid ? '✅ Valid' : '❌ Invalid'}`)
      
      // 5. Test actual task creation (dry run)
      log('\n🧪 5. DRY RUN TASK CREATION TEST')
      log('-'.repeat(40))
      
      if (isValidUuid) {
        try {
          log(`Attempting to create default tasks for project: ${testProjectId}`)
          log(`Client type: ${testClientType}`)
          
          // This will run the actual createDefaultTasks function
          await projectsApi.createDefaultTasks(testProjectId, testClientType)
          log('✅ Task creation completed successfully')
        } catch (error) {
          log(`❌ Task creation failed: ${error}`)
          if (error instanceof Error) {
            log(`Error message: ${error.message}`)
            if (error.stack) {
              log(`Stack trace: ${error.stack}`)
            }
          }
        }
      } else {
        log('❌ Skipping task creation test due to invalid UUID')
      }
      
      log('\n' + '='.repeat(60))
      log('🏁 DIAGNOSTICS COMPLETE')
      
    } catch (error) {
      log(`❌ Diagnostics failed: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testSpecificClientType = async (clientType: ClientType) => {
    setIsLoading(true)
    clearResults()
    
    try {
      log(`🧪 TESTING CLIENT TYPE: ${clientType}`)
      log('=' .repeat(40))
      
      const templates = getDefaultTasksForClientType(clientType)
      log(`Templates found: ${templates.length}`)
      
      if (templates.length > 0) {
        templates.forEach((template, index) => {
          log(`${index + 1}. ${template.title}`)
          log(`   Role: ${template.assigned_role}`)
          log(`   Type: ${template.isProjectTask ? 'Project' : 'Shoot'} task`)
          log(`   Priority: ${template.priority}`)
          log(`   Order: ${template.order}`)
          log('')
        })
        
        // Test with the test project ID
        if (testProjectId) {
          log(`Testing task creation for project: ${testProjectId}`)
          await projectsApi.createDefaultTasks(testProjectId, clientType)
          log('✅ Task creation test completed')
        }
      } else {
        log(`❌ No templates found for client type: ${clientType}`)
      }
      
    } catch (error) {
      log(`❌ Test failed: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Default Task Creation Diagnostics</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Controls */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Test Project ID:</label>
            <input
              type="text"
              value={testProjectId}
              onChange={(e) => setTestProjectId(e.target.value)}
              className="w-full p-2 border rounded"
              placeholder="Enter a valid UUID"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Test Client Type:</label>
            <select
              value={testClientType}
              onChange={(e) => setTestClientType(e.target.value as ClientType)}
              className="w-full p-2 border rounded"
            >
              {CLIENT_TYPES.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>
          
          <div className="space-y-2">
            <button
              onClick={runDiagnostics}
              disabled={isLoading}
              className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {isLoading ? 'Running Diagnostics...' : 'Run Full Diagnostics'}
            </button>
            
            <button
              onClick={() => testSpecificClientType(testClientType)}
              disabled={isLoading}
              className="w-full bg-green-500 text-white p-2 rounded hover:bg-green-600 disabled:opacity-50"
            >
              {isLoading ? 'Testing...' : `Test ${testClientType} Client Type`}
            </button>
            
            <button
              onClick={clearResults}
              className="w-full bg-gray-500 text-white p-2 rounded hover:bg-gray-600"
            >
              Clear Results
            </button>
          </div>
          
          <div className="bg-yellow-50 p-4 rounded border">
            <h3 className="font-semibold text-yellow-800 mb-2">Quick Tests:</h3>
            <div className="space-y-1">
              {CLIENT_TYPES.slice(0, 5).map(type => (
                <button
                  key={type}
                  onClick={() => testSpecificClientType(type)}
                  disabled={isLoading}
                  className="block w-full text-left p-1 text-sm bg-yellow-100 hover:bg-yellow-200 rounded disabled:opacity-50"
                >
                  Test {type}
                </button>
              ))}
            </div>
          </div>
        </div>
        
        {/* Results */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Diagnostic Results</h2>
          <div className="bg-gray-100 p-4 rounded h-96 overflow-y-auto">
            <pre className="text-sm whitespace-pre-wrap font-mono">
              {results || 'Click "Run Full Diagnostics" to start...'}
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}