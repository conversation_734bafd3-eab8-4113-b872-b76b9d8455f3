import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { Toaster } from "react-hot-toast";
import { AuthProvider } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { PWAInstall } from "@/components/ui/pwa-install";
import { Inter } from "next/font/google";
import "@/lib/init-background-jobs"; // Initialize background jobs

// Register service worker for performance optimization
if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration)
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError)
      })
  })
}

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Cymatics - Drone Service Management",
  description: "Streamline your drone service operations with comprehensive project and client management",
  manifest: "/manifest.json",
  themeColor: "#3b82f6",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Cymatics",
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: "website",
    siteName: "Cymatics",
    title: "Cymatics - Drone Service Management",
    description: "Streamline your drone service operations with comprehensive project and client management",
  },
  twitter: {
    card: "summary",
    title: "Cymatics - Drone Service Management",
    description: "Streamline your drone service operations with comprehensive project and client management",
  },
  icons: {
    icon: "/icon-192x192.png",
    shortcut: "/icon-192x192.png",
    apple: "/icon-192x192.png",
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`h-full ${inter.variable}`}>
      <body className="font-sans h-full bg-background text-foreground antialiased">
        <AuthProvider>
          <ThemeProvider>
            {children}
            <Toaster />
            <PWAInstall />
          </ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
