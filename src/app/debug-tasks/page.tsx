'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { projectsApi, usersApi } from '@/lib/api'
import { getDefaultTasksForClientType, DEFAULT_TASK_TEMPLATES } from '@/lib/default-tasks'

export default function DebugTasksPage() {
  const [logs, setLogs] = useState<string[]>([])
  const [loading, setLoading] = useState(false)

  const addLog = (message: string) => {
    console.log(message)
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const debugTaskCreation = async () => {
    setLoading(true)
    setLogs([])

    try {
      addLog('=== Starting Task Creation Debug ===')

      // Test 1: Check available client types
      addLog('Available client types in templates:')
      Object.keys(DEFAULT_TASK_TEMPLATES).forEach(clientType => {
        const templates = DEFAULT_TASK_TEMPLATES[clientType]
        addLog(`  - ${clientType}: ${templates.length} templates`)
      })

      // Test 2: Test template retrieval for different client types
      const testClientTypes = ['Wedding', 'Corporate', 'Real Estate', 'Survey']
      for (const clientType of testClientTypes) {
        const templates = getDefaultTasksForClientType(clientType)
        addLog(`Templates for "${clientType}": ${templates.length}`)
        if (templates.length > 0) {
          templates.forEach(template => {
            addLog(`  - ${template.title} (${template.assigned_role}, ${template.isProjectTask ? 'project' : 'shoot'} task)`)
          })
        }
      }

      // Test 3: Check users by role
      addLog('=== Checking Users ===')
      const usersByRole = await usersApi.getUsersByRoles()
      addLog(`Pilots: ${usersByRole.pilot.length}`)
      addLog(`Editors: ${usersByRole.editor.length}`)
      addLog(`Accounts: ${usersByRole.accounts.length}`)

      // Test 4: Check all users
      const allUsers = await usersApi.getAll()
      addLog(`Total users in system: ${allUsers.length}`)
      allUsers.forEach(user => {
        addLog(`  - ${user.name} (${user.email}) - Role: ${user.role}`)
      })

      // Test 5: Try creating tasks for a test project
      addLog('=== Testing Task Creation ===')
      try {
        // Test project-level task creation
        addLog('Testing project-level task creation...')
        await projectsApi.createDefaultTasks(
          'test-project-id',
          'Wedding',
          undefined,
          undefined
        )
        addLog('Project-level task creation completed')

        // Test shoot-level task creation
        addLog('Testing shoot-level task creation...')
        await projectsApi.createDefaultTasks(
          'test-project-id',
          'Wedding',
          new Date().toISOString(),
          'test-shoot-id'
        )
        addLog('Shoot-level task creation completed')
      } catch (error: any) {
        addLog(`Task creation failed: ${error.message}`)
        console.error('Task creation error:', error)
      }

    } catch (error: any) {
      addLog(`Debug failed: ${error.message}`)
      console.error('Debug error:', error)
    } finally {
      setLoading(false)
    }
  }

  const clearLogs = () => {
    setLogs([])
  }

  const cleanupDuplicates = async () => {
    setLoading(true)
    try {
      addLog('=== Cleaning up duplicate tasks ===')
      await projectsApi.cleanupDuplicateTasks('test-project-id')
      addLog('Duplicate cleanup completed')
    } catch (error: any) {
      addLog(`Cleanup failed: ${error.message}`)
      console.error('Cleanup error:', error)
    } finally {
      setLoading(false)
    }
  }

  const fixTaskOrder = async () => {
    setLoading(true)
    try {
      addLog('=== Fixing task order ===')
      await projectsApi.fixTaskOrder()
      addLog('Task order fix completed')
    } catch (error: any) {
      addLog(`Task order fix failed: ${error.message}`)
      console.error('Task order fix error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Debug Task Creation</h1>
        
        <div className="flex gap-4 mb-6">
          <Button
            onClick={debugTaskCreation}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {loading ? 'Running Debug...' : 'Run Debug'}
          </Button>
          <Button
            onClick={cleanupDuplicates}
            disabled={loading}
            className="bg-red-600 hover:bg-red-700"
          >
            {loading ? 'Cleaning...' : 'Cleanup Duplicates'}
          </Button>
          <Button
            onClick={fixTaskOrder}
            disabled={loading}
            className="bg-green-600 hover:bg-green-700"
          >
            {loading ? 'Fixing...' : 'Fix Task Order'}
          </Button>
          <Button
            onClick={clearLogs}
            variant="outline"
            disabled={loading}
          >
            Clear Logs
          </Button>
        </div>

        <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
          {logs.length === 0 ? (
            <div className="text-gray-500">Click "Run Debug" to start debugging task creation...</div>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="mb-1">
                {log}
              </div>
            ))
          )}
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h3 className="font-semibold text-yellow-800 mb-2">Debug Information:</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• This page helps debug why default tasks are not being created</li>
            <li>• Check if client types match exactly (case-sensitive)</li>
            <li>• Verify users exist in the system with appropriate roles</li>
            <li>• Monitor console logs for detailed error messages</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
