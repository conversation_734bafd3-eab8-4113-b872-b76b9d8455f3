'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Modal } from '@/components/ui/modal'
import { ScheduleForm } from '@/components/forms/ScheduleForm'
import { ScheduleCard } from '@/components/ui/schedule-card'

import { useUpdateSchedule, useDeleteSchedule } from '@/hooks/useApi'
import { useRealTimeSchedules } from '@/hooks/useRealTimeSchedules'
import { Search, Plus, Calendar, Filter, Grid, List } from 'lucide-react'
import toast from 'react-hot-toast'
import type { Schedule } from '@/types'

export default function SchedulesPage() {
  const { schedules, loading, refresh: refetch, updateScheduleOptimistically } = useRealTimeSchedules({
    onScheduleCreate: (schedule) => {
      console.log('New schedule created in real-time:', schedule.custom_id)
      toast.success(`New schedule created: ${schedule.custom_id}`)
    },
    onScheduleUpdate: (schedule) => {
      console.log('Schedule updated in real-time:', schedule.custom_id)
    },
    onScheduleDelete: (scheduleId) => {
      console.log('Schedule deleted in real-time:', scheduleId)
      toast.info('Schedule deleted')
    }
  })
  const { updateSchedule } = useUpdateSchedule()
  const { deleteSchedule } = useDeleteSchedule()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [dateFilter, setDateFilter] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingSchedule, setEditingSchedule] = useState<Schedule | null>(null)


  const filteredSchedules = schedules?.filter(schedule => {
    const matchesSearch = 
      schedule.project?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      schedule.project?.client?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      schedule.project?.location?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || schedule.status === statusFilter

    let matchesDate = true
    if (dateFilter !== 'all') {
      const scheduleDate = new Date(schedule.scheduled_date)
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)

      switch (dateFilter) {
        case 'today':
          matchesDate = scheduleDate >= today && scheduleDate < tomorrow
          break
        case 'upcoming':
          matchesDate = scheduleDate >= now
          break
        case 'this_week':
          matchesDate = scheduleDate >= today && scheduleDate < nextWeek
          break
        case 'overdue':
          matchesDate = scheduleDate < now && schedule.status === 'scheduled'
          break
      }
    }

    return matchesSearch && matchesStatus && matchesDate
  }) || []

  // Sort schedules by scheduled date
  const sortedSchedules = filteredSchedules.sort((a, b) => 
    new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime()
  )

  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false)
    refetch()
  }

  const handleEditSuccess = () => {
    setEditingSchedule(null)
    refetch()
  }

  const handleDelete = async (schedule: Schedule) => {
    try {
      await deleteSchedule(schedule.id)
      toast.success('Schedule deleted successfully')
      refetch()
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete schedule')
    }
  }




  const handleCancel = async (schedule: Schedule) => {
    if (!confirm(`Are you sure you want to cancel this schedule for ${new Date(schedule.scheduled_date).toLocaleDateString()}? This action can be undone by editing the schedule later.`)) {
      return
    }

    try {
      await updateSchedule(schedule.id, { status: 'cancelled' })
      toast.success('Schedule cancelled successfully')
      refetch()
    } catch (error: any) {
      toast.error(error.message || 'Failed to cancel schedule')
    }
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading schedules...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Schedules</h1>
          <p className="text-gray-600">Schedule and manage drone sessions</p>
        </div>
        <Button onClick={() => setIsCreateModalOpen(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Create Schedule
        </Button>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col lg:flex-row gap-3 items-start lg:items-center justify-between">
        <div className="w-full flex flex-col sm:flex-row gap-3 sm:gap-4 lg:max-w-4xl">
          {/* Search */}
          <div className="relative w-full sm:flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search schedules, projects, or clients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-11"
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="h-11 px-3 py-2 border border-input rounded-md bg-background text-sm w-full sm:w-auto"
          >
            <option value="all">All Status</option>
            <option value="scheduled">Scheduled</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
            <option value="rescheduled">Rescheduled</option>
          </select>

          {/* Date Filter */}
          <select
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="h-11 px-3 py-2 border border-input rounded-md bg-background text-sm w-full sm:w-auto"
          >
            <option value="all">All Dates</option>
            <option value="today">Today</option>
            <option value="upcoming">Upcoming</option>
            <option value="this_week">This Week</option>
            <option value="overdue">Overdue</option>
          </select>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center space-x-2 bg-muted rounded-lg p-1">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('grid')}
            className="h-8 w-8 p-0"
          >
            <Grid className="w-4 h-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="h-8 w-8 p-0"
          >
            <List className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Schedules Grid/List */}
      {sortedSchedules.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <Calendar className="w-6 h-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm || statusFilter !== 'all' || dateFilter !== 'all' ? 'No schedules found' : 'No schedules created'}
          </h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || statusFilter !== 'all' || dateFilter !== 'all'
              ? 'Try adjusting your search terms or filters'
              : 'Get started by creating your first schedule'
            }
          </p>
          {!searchTerm && statusFilter === 'all' && dateFilter === 'all' && (
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Schedule
            </Button>
          )}
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4'
            : 'space-y-3'
        }>
          {sortedSchedules.map((schedule) => (
            <ScheduleCard
              key={schedule.id}
              schedule={schedule}
              onEdit={setEditingSchedule}
              onCancel={handleCancel}
              onDelete={handleDelete}
              compact={viewMode === 'list'}
            />
          ))}
        </div>
      )}

      {/* Stats */}
      {sortedSchedules.length > 0 && (
        <div className="bg-card rounded-lg border border-border p-6">
          <h3 className="text-lg font-semibold mb-4">Schedule Statistics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {sortedSchedules.length}
              </div>
              <div className="text-sm text-muted-foreground">Total</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {sortedSchedules.filter(s => s.status === 'completed').length}
              </div>
              <div className="text-sm text-muted-foreground">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {sortedSchedules.filter(s => s.status === 'scheduled').length}
              </div>
              <div className="text-sm text-muted-foreground">Scheduled</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {sortedSchedules.filter(s =>
                  s.status === 'scheduled' && new Date(s.scheduled_date) < new Date()
                ).length}
              </div>
              <div className="text-sm text-muted-foreground">Overdue</div>
            </div>
          </div>
        </div>
      )}

      {/* Create Schedule Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Schedule"
        size="xl"
      >
        <ScheduleForm
          onSuccess={handleCreateSuccess}
          onCancel={() => setIsCreateModalOpen(false)}
        />
      </Modal>

      {/* Edit Schedule Modal */}
      <Modal
        isOpen={!!editingSchedule}
        onClose={() => setEditingSchedule(null)}
        title="Edit Schedule"
        size="xl"
      >
        {editingSchedule && (
          <ScheduleForm
            schedule={editingSchedule}
            onSuccess={handleEditSuccess}
            onCancel={() => setEditingSchedule(null)}
          />
        )}
      </Modal>
    </div>
  )
}
