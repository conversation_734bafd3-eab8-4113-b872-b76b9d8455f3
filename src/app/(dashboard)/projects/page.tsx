'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Modal } from '@/components/ui/modal'
import dynamic from 'next/dynamic'

// Lazy load heavy form components
const ProjectForm = dynamic(() => import('@/components/forms/ProjectForm').then(mod => ({ default: mod.ProjectForm })), {
  loading: () => <div className="animate-pulse bg-muted rounded-lg h-96" />
})
import { ProjectCard } from '@/components/ui/project-card'
import { useDeleteProject } from '@/hooks/useApi'
import { useRealTimeProjects } from '@/hooks/useRealTimeProjects'
import { Search, Plus, Filter, Grid, List, LayoutGrid, LayoutList, ArrowUpDown } from 'lucide-react'
import toast from 'react-hot-toast'
import type { Project } from '@/types'

export default function ProjectsPage() {
  const { projects, loading, refresh: refetch, updateProjectOptimistically } = useRealTimeProjects({
    onProjectCreate: (project) => {
      console.log('New project created in real-time:', project.name)
      toast.success(`New project created: ${project.name}`)
    },
    onProjectUpdate: (project) => {
      console.log('Project updated in real-time:', project.name)
    },
    onProjectDelete: (projectId) => {
      console.log('Project deleted in real-time:', projectId)
      toast.info('Project deleted')
    }
  })
  const { deleteProject } = useDeleteProject()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('new_to_old')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingProject, setEditingProject] = useState<Project | null>(null)
  const [deletingProjectId, setDeletingProjectId] = useState<string | null>(null)

  const filteredProjects = projects?.filter(project => {
    const matchesSearch =
      project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.client?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.location?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || project.status === statusFilter

    return matchesSearch && matchesStatus
  }).sort((a, b) => {
    switch (sortBy) {
      case 'new_to_old':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      case 'pending_payment':
        return b.amount_pending - a.amount_pending
      case 'project_status':
        return a.status.localeCompare(b.status)
      case 'project_value':
        return b.total_amount - a.total_amount
      default:
        return 0
    }
  }) || []

  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false)
    refetch()
  }

  const handleEditSuccess = () => {
    setEditingProject(null)
    refetch()
  }

  const handleDelete = async (project: Project) => {
    try {
      setDeletingProjectId(project.id)
      await deleteProject(project.id)
      toast.success('Project deleted successfully')
      refetch()
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete project')
    } finally {
      setDeletingProjectId(null)
    }
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent mx-auto"></div>
          <p className="text-sm text-muted-foreground">Loading projects...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
          <div className="space-y-1">
            <h1 className="text-3xl font-bold tracking-tight text-foreground">Projects</h1>
            <p className="text-muted-foreground">Manage your drone service projects</p>
          </div>
          <Button
            onClick={() => setIsCreateModalOpen(true)}
            className="w-full sm:w-auto"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Project
          </Button>
        </div>

        {/* Stats Overview - Modern Minimal Design */}
        {filteredProjects.length > 0 && (
          <div className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-4 mb-6">
            <div className="flex items-center justify-between flex-wrap gap-4">
              <div className="flex items-center justify-between w-full gap-4 sm:gap-8 flex-wrap">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="text-sm text-muted-foreground">Total</span>
                  <span className="font-semibold text-foreground">{filteredProjects.length}</span>
                </div>

                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-muted-foreground">Active</span>
                  <span className="font-semibold text-foreground">{filteredProjects.filter(p => p.status === 'active').length}</span>
                </div>

                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-sm text-muted-foreground">Value</span>
                  <span className="font-semibold text-foreground">₹{filteredProjects.reduce((sum, p) => sum + p.total_amount, 0).toLocaleString()}</span>
                </div>

                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span className="text-sm text-muted-foreground">Pending</span>
                  <span className="font-semibold text-foreground">₹{filteredProjects.reduce((sum, p) => sum + p.amount_pending, 0).toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Search and Filters */}
        <div className="bg-card rounded-lg border border-border p-4 sm:p-6 mb-8">
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            {/* Search */}
            <div className="relative w-full sm:flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search projects, clients, or locations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-11"
              />
            </div>

            {/* Filters and View Toggle */}
            <div className="grid grid-cols-2 sm:flex sm:flex-row gap-3">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="flex h-11 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 w-[120px]"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="on_hold">On Hold</option>
                <option value="cancelled">Cancelled</option>
              </select>

              {/* Sort By Dropdown */}
              <div className="relative">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="flex h-11 rounded-md border border-input bg-background px-3 py-2 pr-8 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 w-full sm:w-[130px] appearance-none"
                >
                  <option value="new_to_old">New to Old</option>
                  <option value="pending_payment">Pending Payment</option>
                  <option value="project_status">Project Status</option>
                  <option value="project_value">Project Value</option>
                </select>
                <ArrowUpDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
              </div>

              {/* View Mode Toggle - Hidden on mobile */}
              <div className="hidden sm:flex items-center bg-muted rounded-md p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-9 px-3"
                >
                  <LayoutGrid className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-9 px-3"
                >
                  <LayoutList className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Projects Grid/List */}
        {filteredProjects.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-3">
              {searchTerm || statusFilter !== 'all' ? 'No projects found' : 'No projects yet'}
            </h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your search terms or filters to find what you\'re looking for'
                : 'Get started by creating your first project to begin managing your drone services'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <Button onClick={() => setIsCreateModalOpen(true)} size="lg">
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Project
              </Button>
            )}
          </div>
        ) : (
          <div className={
            viewMode === 'grid'
              ? 'grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 xl:grid-cols-3 gap-3 sm:gap-4'
              : 'hidden sm:block space-y-3'
          }>
            {filteredProjects.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                onEdit={setEditingProject}
                onDelete={handleDelete}
                viewMode={viewMode}
              />
            ))}
          </div>
        )}

        {/* Mobile Grid View (when list is selected) */}
        {viewMode === 'list' && (
          <div className="sm:hidden grid grid-cols-1 gap-4">
            {filteredProjects.map((project) => (
              <ProjectCard
                key={`mobile-${project.id}`}
                project={project}
                onEdit={setEditingProject}
                onDelete={handleDelete}
                viewMode="grid"
              />
            ))}
          </div>
        )}



        {/* Create Project Modal */}
        <Modal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          title="Create New Project"
          size="lg"
        >
          <ProjectForm
            onSuccess={handleCreateSuccess}
            onCancel={() => setIsCreateModalOpen(false)}
          />
        </Modal>

        {/* Edit Project Modal */}
        <Modal
          isOpen={!!editingProject}
          onClose={() => setEditingProject(null)}
          title="Edit Project"
          size="lg"
        >
          {editingProject && (
            <ProjectForm
              project={editingProject}
              onSuccess={handleEditSuccess}
              onCancel={() => setEditingProject(null)}
            />
          )}
        </Modal>
      </div>
    </div>
  )
}
