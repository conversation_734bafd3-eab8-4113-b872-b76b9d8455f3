'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import {
  LayoutDashboard,
  Users,
  FolderOpen,
  Calendar,
  CalendarDays,
  DollarSign,
  CheckSquare,
  CreditCard,
  Map,
  Settings,
  LogOut,
  Menu,
  ChevronLeft,
  Building2,
  UserCog,
} from 'lucide-react'
import toast from 'react-hot-toast'
import { NotificationBell } from '@/components/ui/notification-bell'
import { RealtimeIndicator } from '@/components/ui/realtime-indicator'
import { PreventRefresh } from '@/components/ui/prevent-refresh'

const baseNavigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'Clients', href: '/clients', icon: Users },
  { name: 'Vendors', href: '/vendors', icon: Building2 },
  { name: 'Projects', href: '/projects', icon: FolderOpen },
  { name: 'Schedules', href: '/schedules', icon: Calendar },
  { name: 'Calendar', href: '/calendar', icon: CalendarDays },
  { name: 'Finances', href: '/finances', icon: DollarSign },
  { name: 'Tasks', href: '/tasks', icon: CheckSquare },
  { name: 'Map', href: '/map', icon: Map },
  { name: 'Settings', href: '/settings', icon: Settings },
]

const adminNavigation = [
  { name: 'Task Management', href: '/admin/tasks', icon: UserCog, adminOnly: true },
]

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [isExpanded, setIsExpanded] = useState(false)

  // Create navigation based on user role
  const isAdminOrManager = user?.role === 'admin' || user?.role === 'manager'
  const navigation = isAdminOrManager
    ? [...baseNavigation, ...adminNavigation]
    : baseNavigation

  // Load sidebar state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('sidebar-expanded')
    if (savedState !== null) {
      setIsExpanded(JSON.parse(savedState))
    }
  }, [])

  // Save sidebar state to localStorage when it changes
  const toggleSidebar = () => {
    const newState = !isExpanded
    setIsExpanded(newState)
    localStorage.setItem('sidebar-expanded', JSON.stringify(newState))
  }

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('Signed out successfully')
      router.push('/login')
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign out')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" suppressHydrationWarning={true}>
        <div className="text-center" suppressHydrationWarning={true}>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" suppressHydrationWarning={true}></div>
          <p className="mt-4 text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-background" suppressHydrationWarning={true}>
      {/* Sidebar */}
      <div className={`fixed left-0 top-0 ${isExpanded ? 'w-60' : 'w-16'} bg-card backdrop-blur supports-[backdrop-filter]:bg-card/80 shadow-lg border-r border-border transition-all duration-300 flex flex-col h-screen z-30 group/sidebar`}>
        {/* Header */}
        <div className={`${isExpanded ? 'p-4' : 'p-3'} border-b border-border flex-shrink-0`}>
          <div className="flex items-center justify-between">
            {isExpanded ? (
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div>
                    <h1 className="text-lg font-semibold text-foreground">Cymatics</h1>
                    <p className="text-xs text-muted-foreground">Drone Service Management</p>
                  </div>
                  <RealtimeIndicator showText size="sm" />
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center space-y-2">
                <div className="w-8 h-8 rounded-md bg-primary text-primary-foreground flex items-center justify-center">
                  <span className="font-bold text-sm">C</span>
                </div>
                <RealtimeIndicator size="sm" />
              </div>
            )}
          </div>
        </div>

        {/* Arrow Toggle Button */}
        <button
          onClick={toggleSidebar}
          className={`absolute top-4 ${isExpanded ? '-right-1.5' : '-right-1.5'}
            w-3 h-6 sm:w-3.5 sm:h-7 bg-background border border-border rounded-r-sm shadow
            hover:bg-muted transition-all duration-300 z-40
            flex items-center justify-center group
            opacity-0 group-hover/sidebar:opacity-100 translate-x-2 group-hover/sidebar:translate-x-0`}
          aria-label={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
        >
          <ChevronLeft
            className={`w-2.5 h-2.5 sm:w-3 sm:h-3 text-muted-foreground group-hover:text-foreground transition-transform duration-200 ${
              isExpanded ? '' : 'rotate-180'
            }`}
          />
        </button>

        {/* Navigation - Flexible area */}
        <nav className="mt-4 flex-1 overflow-y-auto">
          <div className={`${isExpanded ? 'px-3' : 'px-2'} flex flex-col gap-1.5`}>
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`w-full flex items-center ${isExpanded ? 'px-3 py-2.5' : 'px-2 py-2.5 justify-center'} text-sm font-medium rounded-lg transition-colors ${
                    isActive ? 'bg-muted text-foreground' : 'text-muted-foreground hover:bg-muted'
                  }`}
                  title={!isExpanded ? item.name : undefined}
                >
                  <item.icon className={`w-5 h-5 ${isExpanded ? 'mr-3' : ''}`} />
                  {isExpanded && (
                    <span className={`${isActive ? 'font-medium' : ''}`}>
                      {item.name}
                    </span>
                  )}
                </Link>
              )
            })}
          </div>
        </nav>

        {/* User Info - Fixed at bottom */}
        <div className={`${isExpanded ? 'p-3' : 'p-2'} border-t border-border flex-shrink-0`}>
          {isExpanded ? (
            <div className="space-y-3">
              {/* Notification Bell - Full width when expanded */}
              <div className="flex justify-center">
                <NotificationBell />
              </div>

              <div className="flex items-center space-x-3">
                <div className="w-9 h-9 rounded-md bg-primary text-primary-foreground flex items-center justify-center">
                  <span className="font-bold text-xs">
                    {user.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-semibold text-card-foreground truncate">{user.name}</p>
                  <p className="text-xs text-muted-foreground capitalize">{user.role}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSignOut}
                  className="p-2 hover:bg-destructive/10 hover:border-destructive hover:text-destructive"
                >
                  <LogOut className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center space-y-3">
              {/* Notification Bell - Centered when collapsed */}
              <NotificationBell />

              <div className="w-9 h-9 rounded-md bg-primary text-primary-foreground flex items-center justify-center">
                <span className="font-bold text-xs">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSignOut}
                className="p-2 hover:bg-destructive/10 hover:border-destructive hover:text-destructive"
                title="Sign Out"
              >
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className={`${isExpanded ? 'ml-60' : 'ml-16'} transition-all duration-300 min-h-screen bg-background`}>
        <main className="p-4 md:p-6 lg:p-8 relative">
          <div className="relative z-10">
            {children}
          </div>
        </main>
        <PreventRefresh />
      </div>

      {/* Mobile bottom navigation */}
      <nav className="fixed bottom-0 inset-x-0 z-40 bg-card border-t border-border shadow hidden md:hidden">
        <ul className="grid grid-cols-5">
          {[
            { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
            { name: 'Projects', href: '/projects', icon: FolderOpen },
            { name: 'Schedules', href: '/schedules', icon: Calendar },
            { name: 'Tasks', href: '/tasks', icon: CheckSquare },
            { name: 'Settings', href: '/settings', icon: Settings },
          ].map((item) => {
            const isActive = pathname === item.href
            return (
              <li key={item.name}>
                <Link href={item.href} className={`flex flex-col items-center justify-center py-2.5 text-xs ${isActive ? 'text-foreground' : 'text-muted-foreground'}`}>
                  <item.icon className="h-5 w-5" />
                  <span className="mt-0.5">{item.name}</span>
                </Link>
              </li>
            )
          })}
        </ul>
      </nav>
    </div>
  )
}
