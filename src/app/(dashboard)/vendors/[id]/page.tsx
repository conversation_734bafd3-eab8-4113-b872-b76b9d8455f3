'use client'

import { useState, useEffect, useCallback } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'

import { Modal } from '@/components/ui/modal'
import { VendorForm } from '@/components/forms/VendorForm'
import { PaymentProgressBar } from '@/components/ui/payment-progress-bar'
import { supabase } from '@/lib/supabase'
import {
  ArrowLeft,
  Edit,
  Mail,
  Phone,
  MapPin,
  User,
  Briefcase,
  Calendar,
  DollarSign,
  TrendingUp,
  BarChart3,
  Eye,
  EyeOff,
  Building2,
  CreditCard,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react'
import Link from 'next/link'
import toast from 'react-hot-toast'
import { InteractiveSpendingChart } from '@/components/ui/interactive-spending-chart'
import { calculateVendorPaymentStatus, calculateOutsourcingExpenses } from '@/lib/project-calculations'
import type { OutsourcingVendor, Shoot, Project } from '@/types'

interface VendorProject extends Shoot {
  project?: Project & {
    payments?: Array<{
      id: string
      amount: number
      payment_date: string
      payment_method: string
      reference_number?: string
      notes?: string
    }>
  }
}

interface VendorPayment {
  id: string
  project_id: string
  project_name: string
  client_name: string
  shoot_date: string
  outsourcing_cost: number
  payment_status: 'pending' | 'paid' | 'overdue' | 'cancelled'
  payment_amount?: number
  payment_due_date?: string
  payment_date?: string
  payment_notes?: string
}

interface VendorStats {
  totalProjects: number
  totalSpent: number
  averageCostPerProject: number
  activeProjects: number
  completedProjects: number
  lastProjectDate: string | null
  monthlySpending: { month: string; amount: number }[]
  costByStatus: { status: string; amount: number; count: number }[]
  paymentStats: {
    totalPaid: number
    totalPending: number
    paidProjects: number
    pendingProjects: number
  }
}

export default function VendorDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const vendorId = params.id as string

  const [vendor, setVendor] = useState<OutsourcingVendor | null>(null)
  const [projects, setProjects] = useState<VendorProject[]>([])
  const [vendorPayments, setVendorPayments] = useState<VendorPayment[]>([])
  const [stats, setStats] = useState<VendorStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [projectPaymentMap, setProjectPaymentMap] = useState<Map<string, any>>(new Map())




  // Fetch vendor details
  const fetchVendorDetails = useCallback(async () => {
    try {
      setLoading(true)

      // Fetch vendor info
      const { data: vendorData, error: vendorError } = await supabase
        .from('outsourcing_vendors')
        .select('*')
        .eq('id', vendorId)
        .single()

      if (vendorError) {
        throw vendorError
      }
      setVendor(vendorData)

      // Fetch vendor's projects with payment information using API endpoint.
      // Include auth cookie in request so the route has a session.
      const base = typeof window !== 'undefined' ? window.location.origin : ''
      const response = await fetch(`${base}/api/vendor-shoots?vendorId=${encodeURIComponent(vendorId)}`, {
        credentials: 'include',
        headers: {
          'accept': 'application/json'
        }
      })
      let apiData: any = {}
      try { apiData = await response.json() } catch {}

      if (!response.ok) {
        console.error('Vendor shoots API failed', { status: response.status, apiData })
        throw new Error(apiData?.error || `Failed to fetch shoots data (${response.status})`)
      }

      const shootsData = apiData.shoots || []
      setProjects(shootsData)

      // Group shoots by project to calculate per-project payment status
      const projectGroups = shootsData.reduce((acc: any, shoot: any) => {
        const projectId = shoot.project_id
        if (!acc[projectId]) {
          acc[projectId] = {
            shoots: [],
            expenses: shoot.project?.expenses || []
          }
        }
        acc[projectId].shoots.push(shoot)
        return acc
      }, {})

      // Process vendor payment information with calculated payment status
      const payments: VendorPayment[] = []
      
      // Group shoots by project and calculate payment status for each
      const projectPaymentMap = new Map()
      
      Object.values(projectGroups).forEach((group: any) => {
        const { shoots, expenses } = group
        
        // Filter shoots for this specific vendor
        const vendorShoots = shoots.map((shoot: any) => {
          const vendorEntry = shoot.vendors?.find((sv: any) => sv.vendor_id === vendorId)
          return {
            ...shoot,
            outsourcing_cost: vendorEntry?.cost || 0,
            vendors: vendorEntry ? [{ cost: vendorEntry.cost }] : []
          }
        }).filter((shoot: any) => shoot.outsourcing_cost > 0)

        if (vendorShoots.length === 0) return

        // Calculate outsourcing expenses for this vendor in this project
        const vendorOutsourcingExpenses = calculateOutsourcingExpenses(
          expenses.filter((exp: any) => exp.vendor_id === vendorId)
        )

        // Calculate payment status for this project
        const paymentStatus = calculateVendorPaymentStatus(vendorShoots, vendorOutsourcingExpenses)
        
        // Store payment status for this project
        vendorShoots.forEach((shoot: any) => {
          projectPaymentMap.set(shoot.project_id, {
            status: paymentStatus.status,
            totalPaid: paymentStatus.totalPaid,
            totalOwed: paymentStatus.totalOwed
          })
        })
      })

      // Store projectPaymentMap in state so it can be used in the render
      setProjectPaymentMap(projectPaymentMap)

      // Create payment records with calculated status
      shootsData.forEach((shoot: any) => {
        const vendorEntry = shoot.vendors?.find((sv: any) => sv.vendor_id === vendorId)
        const cost = vendorEntry?.cost || 0
        
        if (cost > 0) {
          const projectPayment = projectPaymentMap.get(shoot.project_id)
          
          payments.push({
            id: shoot.id,
            project_id: shoot.project_id,
            project_name: shoot.project?.name || 'Unknown Project',
            client_name: shoot.project?.client?.name || 'Unknown Client',
            shoot_date: shoot.scheduled_date,
            outsourcing_cost: cost,
            payment_status: projectPayment?.status || 'pending',
            payment_amount: projectPayment?.totalPaid || 0,
            payment_due_date: shoot.project?.vendor_payment_due_date,
            payment_date: shoot.project?.vendor_payment_date,
            payment_notes: shoot.project?.vendor_payment_notes,
          })
        }
      })
      
      setVendorPayments(payments)

      // Calculate stats
      const totalProjects = shootsData?.length || 0
      const totalSpent = shootsData?.reduce((sum: number, shoot: any) => {
        // Find the specific vendor entry from the schedule_vendors junction table
        const vendorEntry = shoot.vendors?.find((sv: any) => sv.vendor_id === vendorId)
        const cost = vendorEntry?.cost || 0
        return sum + cost
      }, 0) || 0
      const averageCostPerProject = totalProjects > 0 ? totalSpent / totalProjects : 0
      const activeProjects = shootsData?.filter((p: any) => p.status === 'scheduled').length || 0
      const completedProjects = shootsData?.filter((p: any) => p.status === 'completed').length || 0
      const lastProjectDate = shootsData?.[0]?.scheduled_date || null

      // Calculate monthly spending
      const monthlySpending = shootsData?.reduce((acc: any[], shoot: any) => {
        // Find the specific vendor entry from the schedule_vendors junction table
        const vendorEntry = shoot.vendors?.find((sv: any) => sv.vendor_id === vendorId)
        const cost = vendorEntry?.cost || 0
        
        if (cost > 0 && shoot.scheduled_date) {
          const month = new Date(shoot.scheduled_date).toLocaleDateString('en-IN', { year: 'numeric', month: 'short' })
          const existing = acc.find((item: { month: string; amount: number }) => item.month === month)
          if (existing) {
            existing.amount += cost
          } else {
            acc.push({ month, amount: cost })
          }
        }
        return acc
      }, [] as { month: string; amount: number }[]) || []

      // Calculate cost by status
      const costByStatus = shootsData?.reduce((acc: any[], shoot: any) => {
        // Find the specific vendor entry from the schedule_vendors junction table
        const vendorEntry = shoot.vendors?.find((sv: any) => sv.vendor_id === vendorId)
        const cost = vendorEntry?.cost || 0
        
        if (cost > 0 && shoot.status) {
          const existing = acc.find((item: { status: string; amount: number; count: number }) => item.status === shoot.status)
          if (existing) {
            existing.amount += cost
            existing.count += 1
          } else {
            acc.push({ status: shoot.status, amount: cost, count: 1 })
          }
        }
        return acc
      }, [] as { status: string; amount: number; count: number }[]) || []

      // Calculate payment stats using actual outsourcing expenses data

      let totalPaidAmount = 0
      let totalPendingAmount = 0
      let paidProjectsCount = 0
      let pendingProjectsCount = 0

      // Calculate payment status for each project
      Object.values(projectGroups).forEach((group: any) => {
        const { shoots, expenses } = group
        
        // Filter shoots for this specific vendor
        const vendorShoots = shoots.map((shoot: any) => {
          const vendorEntry = shoot.vendors?.find((sv: any) => sv.vendor_id === vendorId)
          return {
            ...shoot,
            outsourcing_cost: vendorEntry?.cost || 0,
            vendors: vendorEntry ? [{ cost: vendorEntry.cost }] : []
          }
        }).filter((shoot: any) => shoot.outsourcing_cost > 0)

        if (vendorShoots.length === 0) return

        // Calculate outsourcing expenses for this vendor in this project
        const vendorOutsourcingExpenses = calculateOutsourcingExpenses(
          expenses.filter((exp: any) => exp.vendor_id === vendorId)
        )

        // Calculate payment status for this project
        const paymentStatus = calculateVendorPaymentStatus(vendorShoots, vendorOutsourcingExpenses)
        
        if (paymentStatus.status === 'paid') {
          totalPaidAmount += paymentStatus.totalOwed
          paidProjectsCount += 1
        } else {
          totalPendingAmount += (paymentStatus.totalOwed - paymentStatus.totalPaid)
          pendingProjectsCount += 1
          if (paymentStatus.totalPaid > 0) {
            totalPaidAmount += paymentStatus.totalPaid
          }
        }
      })

      const paymentStats = {
        totalPaid: totalPaidAmount,
        totalPending: totalPendingAmount,
        paidProjects: paidProjectsCount,
        pendingProjects: pendingProjectsCount
      }

      setStats({
        totalProjects,
        totalSpent,
        averageCostPerProject,
        activeProjects,
        completedProjects,
        lastProjectDate,
        monthlySpending: monthlySpending.sort((a: { month: string; amount: number }, b: { month: string; amount: number }) => new Date(a.month).getTime() - new Date(b.month).getTime()),
        costByStatus,
        paymentStats
      })

    } catch (error) {
      console.error('Failed to load vendor details:', error)
      toast.error('Failed to load vendor details')
    } finally {
      setLoading(false)
    }
  }, [vendorId])

  useEffect(() => {
    if (vendorId) {
      fetchVendorDetails()
    }
  }, [vendorId, fetchVendorDetails])

  // Set up real-time subscription to refresh when project payment status changes
  useEffect(() => {
    if (!vendorId) return

    // Subscribe to changes in projects table for vendor payment status updates
    const channel = supabase
      .channel('vendor-payment-updates')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'projects',
          filter: `vendor_payment_status=neq.null`
        },
        (payload) => {
          console.log('Project payment status updated:', payload)
          // Refresh vendor details when any project payment status changes
          fetchVendorDetails()
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'expenses',
          filter: `category=eq.outsourcing`
        },
        (payload) => {
          console.log('Outsourcing expense added:', payload)
          // Refresh vendor details when a new outsourcing expense is added
          fetchVendorDetails()
        }
      )
      .subscribe()

    // Also refresh when page regains focus (user switches back to tab)
    const handleFocus = () => {
      fetchVendorDetails()
    }

    window.addEventListener('focus', handleFocus)

    return () => {
      supabase.removeChannel(channel)
      window.removeEventListener('focus', handleFocus)
    }
  }, [vendorId, fetchVendorDetails])



  const handleEditSuccess = () => {
    setIsEditModalOpen(false)
    fetchVendorDetails()
  }

  const toggleVendorStatus = async () => {
    if (!vendor) return

    try {
      const { error } = await supabase
        .from('outsourcing_vendors')
        .update({ 
          is_active: !vendor.is_active, 
          updated_at: new Date().toISOString() 
        })
        .eq('id', vendor.id)
      
      if (error) throw error
      toast.success(`Vendor ${vendor.is_active ? 'deactivated' : 'activated'} successfully`)
      fetchVendorDetails()
    } catch (error: any) {
      toast.error(error.message || 'Failed to update vendor status')
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading vendor details...</p>
        </div>
      </div>
    )
  }

  if (!vendor) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">Vendor not found</h3>
          <p className="text-muted-foreground mb-6">The vendor you're looking for doesn't exist.</p>
          <Link href="/vendors">
            <Button variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Vendors
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-5 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="flex flex-col gap-4">
        {/* Back Button */}
        <div className="flex justify-start">
          <Link href="/vendors">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </Link>
        </div>

        {/* Centered Vendor Info */}
        <div className="flex flex-col items-center text-center gap-3">
          {/* Vendor Avatar */}
          <div
            className={
              `w-16 h-16 bg-gradient-to-br rounded-xl flex items-center justify-center flex-shrink-0 ${
                vendor.is_active ? 'from-purple-500 to-purple-600' : 'from-gray-400 to-gray-500'
              }`
            }
          >
            <span className="text-white font-semibold text-2xl">
              {vendor.name.charAt(0).toUpperCase()}
            </span>
          </div>

          {/* Vendor Info */}
          <div className="flex flex-col items-center">
            <div className="flex items-center gap-3 mb-1.5">
              <h1 className="text-2xl sm:text-3xl font-semibold text-foreground">{vendor.name}</h1>
              <span
                className={
                  `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    vendor.is_active
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                  }`
                }
              >
                {vendor.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
            {(vendor as any)?.custom_id && (
              <div className="mb-1.5">
                <span
                  className="inline-block px-2 py-0.5 rounded border border-gray-200 dark:border-gray-700 text-xs text-muted-foreground bg-gray-50 dark:bg-gray-900/40 font-mono"
                  title="Vendor ID"
                >
                  {(vendor as any).custom_id}
                </span>
              </div>
            )}
            <div className="flex flex-wrap items-center justify-center gap-x-4 gap-y-1 text-sm text-muted-foreground mb-1.5">
              {vendor.specialization && (
                <div className="flex items-center gap-1.5">
                  <Briefcase className="w-4 h-4" />
                  <span>{vendor.specialization}</span>
                </div>
              )}
              {vendor.contact_person && (
                <div className="flex items-center gap-1.5">
                  <User className="w-4 h-4" />
                  <span>{vendor.contact_person}</span>
                </div>
              )}
              {vendor.email && (
                <div className="flex items-center gap-1.5">
                  <Mail className="w-4 h-4" />
                  <span>{vendor.email}</span>
                </div>
              )}
              {vendor.phone && (
                <div className="flex items-center gap-1.5">
                  <Phone className="w-4 h-4" />
                  <span>{vendor.phone}</span>
                </div>
              )}
            </div>

            <p className="text-sm text-muted-foreground">
              Member since {formatDate(vendor.created_at)}
            </p>

            {/* Action Buttons */}
            <div className="flex gap-2 mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={toggleVendorStatus}
                className="flex items-center gap-2"
              >
                {vendor.is_active ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                {vendor.is_active ? 'Deactivate' : 'Activate'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditModalOpen(true)}
                className="flex items-center gap-2"
              >
                <Edit className="w-4 h-4" />
                Edit
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          <div className="rounded-xl border border-border/60 bg-card/60 p-4">
            <div className="flex items-center justify-between gap-3">
              <div>
                <p className="text-xs font-medium text-muted-foreground">Total Projects</p>
                <p className="text-2xl font-semibold text-foreground">{stats.totalProjects}</p>
              </div>
              <div className="rounded-md p-2 bg-muted/60">
                <BarChart3 className="w-5 h-5 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="rounded-xl border border-border/60 bg-card/60 p-4">
            <div className="flex items-center justify-between gap-3">
              <div>
                <p className="text-xs font-medium text-muted-foreground">Total Spent</p>
                <p className="text-2xl font-semibold text-foreground">{formatCurrency(stats.totalSpent)}</p>
              </div>
              <div className="rounded-md p-2 bg-muted/60">
                <DollarSign className="w-5 h-5 text-green-500" />
              </div>
            </div>
          </div>

          <div className="rounded-xl border border-border/60 bg-card/60 p-4">
            <div className="flex items-center justify-between gap-3">
              <div>
                <p className="text-xs font-medium text-muted-foreground">Avg Cost/Project</p>
                <p className="text-2xl font-semibold text-foreground">{formatCurrency(stats.averageCostPerProject)}</p>
              </div>
              <div className="rounded-md p-2 bg-muted/60">
                <TrendingUp className="w-5 h-5 text-purple-500" />
              </div>
            </div>
          </div>

          <div className="rounded-xl border border-border/60 bg-card/60 p-4">
            <div className="flex items-center justify-between gap-3">
              <div>
                <p className="text-xs font-medium text-muted-foreground">Active Projects</p>
                <p className="text-2xl font-semibold text-foreground">{stats.activeProjects}</p>
              </div>
              <div className="rounded-md p-2 bg-muted/60">
                <Calendar className="w-5 h-5 text-orange-500" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Payment Analytics */}
      {stats && (
        <div className="rounded-xl border border-border/60 bg-card/60 p-5">
          <h2 className="text-lg font-semibold text-foreground mb-4">Payment Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="rounded-lg border border-green-500/20 bg-green-500/5 p-4">
              <div className="flex items-center justify-between gap-3">
                <div>
                  <p className="text-xs font-medium text-green-600 dark:text-green-400">Paid</p>
                  <p className="text-xl font-semibold text-green-700 dark:text-green-300">{formatCurrency(stats.paymentStats.totalPaid)}</p>
                  <p className="text-[11px] text-green-600/80 dark:text-green-400/80">{stats.paymentStats.paidProjects} projects</p>
                </div>
                <div className="rounded-md p-2 bg-green-500/10">
                  <DollarSign className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
              </div>
            </div>

            <div className="rounded-lg border border-amber-500/20 bg-amber-500/5 p-4">
              <div className="flex items-center justify-between gap-3">
                <div>
                  <p className="text-xs font-medium text-amber-600 dark:text-amber-400">Pending</p>
                  <p className="text-xl font-semibold text-amber-700 dark:text-amber-300">{formatCurrency(stats.paymentStats.totalPending)}</p>
                  <p className="text-[11px] text-amber-600/80 dark:text-amber-400/80">{stats.paymentStats.pendingProjects} projects</p>
                </div>
                <div className="rounded-md p-2 bg-amber-500/10">
                  <Calendar className="w-5 h-5 text-amber-600 dark:text-amber-400" />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}





      {/* Project History */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-foreground">Project History</h2>
          <span className="text-sm text-muted-foreground">
            {projects.length} project{projects.length !== 1 ? 's' : ''}
          </span>
        </div>

        {projects.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">No outsourced projects found</h3>
            <p className="text-muted-foreground">
              This vendor hasn't been assigned to any outsourced shoots yet, or the shoots don't have outsourcing costs set.
            </p>
            <div className="mt-4 text-sm text-muted-foreground">
              <p>To link this vendor to projects:</p>
              <ol className="list-decimal list-inside mt-2 space-y-1">
                <li>Create or edit a shoot in a project</li>
                <li>Check "This is an outsourced shoot"</li>
                <li>Select this vendor and set the outsourcing cost</li>
              </ol>
            </div>
          </div>
        ) : (
          <div className="space-y-2.5">
            {projects.map((project) => (
              <Link
                key={project.id}
                href={`/projects/${project.project?.id}`}
                className="group block rounded-xl border border-border/60 bg-card/60 px-4 py-3 sm:px-5 sm:py-4 transition-colors hover:bg-card hover:shadow-sm"
                aria-label={`View project ${project.project?.name || 'Untitled Project'}`}
              >
                <div className="grid grid-cols-1 sm:grid-cols-[1fr_auto] items-center gap-3 sm:gap-6">
                  <div className="min-w-0">
                    {/* Title + badges */}
                    <div className="flex items-center gap-2 sm:gap-2.5 mb-1.5">
                      <h3 className="text-base font-medium text-foreground truncate">
                        {project.project?.name || 'Untitled Project'}
                      </h3>
                      <div className="flex items-center gap-1.5 flex-shrink-0">
                        <span className={`inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[11px] font-medium ${
                          project.status === 'completed'
                            ? 'bg-green-100 text-green-700 dark:bg-green-900/25 dark:text-green-400'
                            : project.status === 'scheduled'
                            ? 'bg-amber-100 text-amber-700 dark:bg-amber-900/25 dark:text-amber-400'
                            : 'bg-gray-100 text-gray-700 dark:bg-gray-900/25 dark:text-gray-400'
                        }`}>
                          {project.status?.replace('_', ' ') || 'Unknown'}
                        </span>

                        {project.project?.vendor_payment_status && (
                          <span className={`inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[11px] font-medium ${
                            project.project.vendor_payment_status === 'paid'
                              ? 'bg-green-100 text-green-700 dark:bg-green-900/25 dark:text-green-400'
                              : project.project.vendor_payment_status === 'pending'
                              ? 'bg-amber-100 text-amber-700 dark:bg-amber-900/25 dark:text-amber-400'
                              : project.project.vendor_payment_status === 'overdue'
                              ? 'bg-red-100 text-red-700 dark:bg-red-900/25 dark:text-red-400'
                              : 'bg-gray-100 text-gray-700 dark:bg-gray-900/25 dark:text-gray-400'
                          }`}>
                            {project.project.vendor_payment_status === 'pending' ? 'Pending' :
                             project.project.vendor_payment_status === 'paid' ? 'Paid' :
                             project.project.vendor_payment_status === 'overdue' ? 'Overdue' :
                             project.project.vendor_payment_status}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Meta */}
                    <div className="flex flex-wrap items-center gap-x-4 gap-y-1 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1.5 min-w-0">
                        <Calendar className="h-3.5 w-3.5 flex-shrink-0" />
                        <span className="truncate">{formatDate(project.scheduled_date)}</span>
                      </div>
                      {project.location && (
                        <div className="flex items-center gap-1.5 min-w-0">
                          <MapPin className="h-3.5 w-3.5 flex-shrink-0" />
                          <span className="truncate">{project.location}</span>
                        </div>
                      )}
                    </div>

                    {project.notes && (
                      <p className="mt-1.5 text-[11px] text-muted-foreground line-clamp-1">
                        {project.notes}
                      </p>
                    )}
                  </div>

                  {/* Amount + progress */}
                  <div className="sm:text-right sm:pl-2">
                      {(() => {
                        const projectPayment = projectPaymentMap.get(project.project_id)
                        
                        if (project.vendor_id === vendorId) {
                          return project.outsourcing_cost ? (
                          <div className="w-full sm:w-56">
                            <div className="text-sm font-semibold text-foreground">
                                  {formatCurrency(project.outsourcing_cost)}
                              </div>
                            <div className="mt-1">
                                <PaymentProgressBar
                                  totalAmount={project.outsourcing_cost}
                                  paidAmount={projectPayment?.totalPaid || 0}
                                  status={projectPayment?.status || 'pending'}
                                  size="sm"
                                />
                              </div>
                            </div>
                          ) : null
                        }

                        const vendorEntry = project.vendors?.find((sv: any) => sv.vendor_id === vendorId)
                        return vendorEntry?.cost ? (
                        <div className="w-full sm:w-56">
                          <div className="text-sm font-semibold text-foreground">
                                {formatCurrency(vendorEntry.cost)}
                            </div>
                          <div className="mt-1">
                              <PaymentProgressBar
                                totalAmount={vendorEntry.cost}
                                paidAmount={projectPayment?.totalPaid || 0}
                                status={projectPayment?.status || 'pending'}
                                size="sm"
                              />
                            </div>
                          </div>
                        ) : null
                      })()}
                    </div>
                  </div>
                    </Link>
            ))}
          </div>
        )}
      </div>

      {/* Financial Analytics */}
      {stats && stats.totalSpent > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Interactive Monthly Spending Chart */}
          <InteractiveSpendingChart
            data={stats.monthlySpending}
            title="Monthly Spending Trend"
          />

          {/* Cost by Project Status */}
          <div className="rounded-xl border border-border/60 bg-card/60 p-5">
            <h2 className="text-lg font-semibold text-foreground mb-4">Cost by Project Status</h2>
            {stats.costByStatus.length > 0 ? (
              <div className="space-y-4">
                {stats.costByStatus.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg border border-border/60 bg-muted/40">
                    <div className="flex items-center gap-3">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        item.status === 'completed'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : item.status === 'in_progress'
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                          : item.status === 'scheduled'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                      }`}>
                        {item.status.replace('_', ' ')}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {item.count} project{item.count !== 1 ? 's' : ''}
                      </span>
                    </div>
                    <span className="text-sm font-semibold text-foreground">
                      {formatCurrency(item.amount)}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground text-center py-8">No cost data available</p>
            )}
          </div>
        </div>
      )}

      {/* Removed verbose Financial Summary to avoid duplication with stats above */}

      {/* Payment History */}
      {vendorPayments.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-lg font-semibold text-foreground mb-6 flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Payment History
          </h2>
          <div className="space-y-2.5">
              {vendorPayments.map((payment) => (
              <Link
                  key={payment.id}
                href={`/projects/${payment.project_id}`}
                className="group block rounded-xl border border-border/60 bg-card/60 px-4 py-3 sm:px-5 sm:py-4 transition-colors hover:bg-card hover:shadow-sm"
                aria-label={`View project ${payment.project_name}`}
              >
                <div className="grid grid-cols-1 sm:grid-cols-[1fr_auto] items-center gap-3 sm:gap-6">
                  <div className="min-w-0">
                    {/* Title + status */}
                    <div className="flex items-center gap-2 sm:gap-2.5 mb-1.5">
                      <h3 className="text-base font-medium text-foreground truncate">{payment.project_name}</h3>
                      <span
                        className={`inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[11px] font-medium ${
                          payment.payment_status === 'paid'
                            ? 'bg-green-100 text-green-700 dark:bg-green-900/25 dark:text-green-400'
                            : payment.payment_status === 'overdue'
                            ? 'bg-red-100 text-red-700 dark:bg-red-900/25 dark:text-red-400'
                            : payment.payment_status === 'cancelled'
                            ? 'bg-gray-100 text-gray-700 dark:bg-gray-900/25 dark:text-gray-400'
                            : 'bg-amber-100 text-amber-700 dark:bg-amber-900/25 dark:text-amber-400'
                        }`}
                      >
                        {payment.payment_status === 'paid' && <CheckCircle className="w-3 h-3" />}
                        {payment.payment_status === 'pending' && <Clock className="w-3 h-3" />}
                        {payment.payment_status === 'overdue' && <AlertCircle className="w-3 h-3" />}
                        {payment.payment_status === 'cancelled' && <XCircle className="w-3 h-3" />}
                        {payment.payment_status.charAt(0).toUpperCase() + payment.payment_status.slice(1)}
                      </span>
                    </div>

                    {/* Meta - compact, no redundant labels */}
                    <div className="flex flex-wrap items-center gap-x-4 gap-y-1 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1.5">
                        <Calendar className="h-3.5 w-3.5" />
                        <span>{new Date(payment.shoot_date).toLocaleDateString()}</span>
                      </div>
                      {payment.payment_status === 'paid' && payment.payment_date && (
                        <div className="flex items-center gap-1.5">
                          <CheckCircle className="h-3.5 w-3.5" />
                          <span>Paid on {new Date(payment.payment_date).toLocaleDateString()}</span>
                        </div>
                      )}
                      {payment.payment_status !== 'paid' && payment.payment_due_date && (
                        <div className="flex items-center gap-1.5">
                          <Clock className="h-3.5 w-3.5" />
                          <span>Due {new Date(payment.payment_due_date).toLocaleDateString()}</span>
                        </div>
                      )}
                    </div>

                      {payment.payment_notes && (
                      <p className="mt-1.5 text-[11px] text-muted-foreground line-clamp-1">
                        {payment.payment_notes}
                      </p>
                      )}
                    </div>

                  {/* Amount + progress */}
                  <div className="sm:text-right sm:pl-2">
                    <div className="text-sm font-semibold text-foreground">
                      {formatCurrency(payment.outsourcing_cost)}
                    </div>
                    <div className="mt-1 w-full sm:w-56">
                    <PaymentProgressBar
                      totalAmount={payment.outsourcing_cost}
                      paidAmount={payment.payment_amount || 0}
                      status={payment.payment_status}
                      size="sm"
                    />
                  </div>
                </div>
                </div>
              </Link>
              ))}
            </div>
        </div>
      )}

      {/* Edit Vendor Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Vendor"
      >
        <VendorForm
          vendor={vendor}
          onSuccess={handleEditSuccess}
          onCancel={() => setIsEditModalOpen(false)}
        />
      </Modal>
    </div>
  )
}
