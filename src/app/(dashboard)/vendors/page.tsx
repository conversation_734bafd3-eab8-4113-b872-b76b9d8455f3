'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Modal } from '@/components/ui/modal'
import { VendorForm } from '@/components/forms/VendorForm'
import { supabase } from '@/lib/supabase'
import { Search, Plus, Edit, Trash2, Mail, Phone, MapPin, User, Briefcase, Eye, EyeOff } from 'lucide-react'
import Link from 'next/link'
import toast from 'react-hot-toast'
import type { OutsourcingVendor } from '@/types'

export default function VendorsPage() {
  const [vendors, setVendors] = useState<OutsourcingVendor[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingVendor, setEditingVendor] = useState<OutsourcingVendor | null>(null)
  const [deletingVendorId, setDeletingVendorId] = useState<string | null>(null)
  const [showInactive, setShowInactive] = useState(false)

  // Fetch vendors
  const fetchVendors = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('outsourcing_vendors')
        .select('*')
        .order('name')
      
      if (error) throw error
      setVendors(data || [])
    } catch (error) {
      console.error('Error fetching vendors:', error)
      toast.error('Failed to load vendors')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchVendors()
  }, [])

  // Filter vendors
  const filteredVendors = vendors
    .filter(vendor => {
      const matchesSearch = vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           vendor.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           vendor.specialization?.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStatus = showInactive || vendor.is_active
      return matchesSearch && matchesStatus
    })

  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false)
    fetchVendors()
  }

  const handleEditSuccess = () => {
    setEditingVendor(null)
    fetchVendors()
  }

  const handleDelete = async (vendor: OutsourcingVendor) => {
    if (!confirm(`Are you sure you want to delete "${vendor.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      setDeletingVendorId(vendor.id)
      const { error } = await supabase
        .from('outsourcing_vendors')
        .delete()
        .eq('id', vendor.id)
      
      if (error) throw error
      toast.success('Vendor deleted successfully')
      fetchVendors()
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete vendor')
    } finally {
      setDeletingVendorId(null)
    }
  }

  const toggleVendorStatus = async (vendor: OutsourcingVendor) => {
    try {
      const { error } = await supabase
        .from('outsourcing_vendors')
        .update({ is_active: !vendor.is_active, updated_at: new Date().toISOString() })
        .eq('id', vendor.id)
      
      if (error) throw error
      toast.success(`Vendor ${vendor.is_active ? 'deactivated' : 'activated'} successfully`)
      fetchVendors()
    } catch (error: any) {
      toast.error(error.message || 'Failed to update vendor status')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading vendors...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 sm:space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-foreground">Outsourcing Vendors</h1>
          <p className="text-sm text-muted-foreground mt-1">Manage your external service providers</p>
        </div>
        <Button onClick={() => setIsCreateModalOpen(true)} className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto">
          <Plus className="w-4 h-4 mr-2" />
          Add Vendor
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-3 sm:items-center">
        <div className="relative flex-1 sm:max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search vendors..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 h-9 border-gray-200 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400"
          />
        </div>

        <div className="flex items-center justify-between sm:justify-end gap-3">
          {/* Results count */}
          <div className="text-sm text-muted-foreground">
            {filteredVendors.length} of {vendors.length}
          </div>

          {/* Show inactive toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowInactive(!showInactive)}
            className="flex items-center gap-2 h-9 px-3 border-gray-200 dark:border-gray-700"
          >
            {showInactive ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
            <span className="text-sm">{showInactive ? 'Hide Inactive' : 'Show Inactive'}</span>
          </Button>
        </div>
      </div>

      {/* Vendors List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
        {filteredVendors.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">
              {searchTerm ? 'No vendors found' : 'No vendors yet'}
            </h3>
            <p className="text-muted-foreground mb-6">
              {searchTerm
                ? 'Try adjusting your search terms'
                : 'Get started by adding your first outsourcing vendor'
              }
            </p>
            {!searchTerm && (
              <Button onClick={() => setIsCreateModalOpen(true)} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Add Vendor
              </Button>
            )}
          </div>
        ) : (
          filteredVendors.map((vendor) => (
            <div key={vendor.id} className={`relative bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 hover:shadow-lg transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600 overflow-hidden group ${!vendor.is_active ? 'opacity-60' : ''}`}>
              {/* Content */}
              <div className="relative z-10">
                {/* Header with Avatar and Name */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {/* Avatar */}
                    <div className={`w-12 h-12 bg-gradient-to-br ${vendor.is_active ? 'from-purple-500 to-purple-600' : 'from-gray-400 to-gray-500'} rounded-xl flex items-center justify-center flex-shrink-0`}>
                      <span className="text-white font-semibold text-lg">
                        {vendor.name.charAt(0).toUpperCase()}
                      </span>
                    </div>

                    {/* Name, Status, and ID */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Link href={`/vendors/${vendor.id}`}>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer transition-colors">
                            {vendor.name}
                          </h3>
                        </Link>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium flex-shrink-0 ${vendor.is_active ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'}`}>
                          {vendor.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      {(vendor as any)?.custom_id && (
                        <div className="mb-1">
                          <span
                            className="inline-block px-1.5 py-0.5 rounded border border-gray-200 dark:border-gray-700 text-[10px] text-muted-foreground bg-gray-50 dark:bg-gray-900/40 font-mono"
                            title="Vendor ID"
                          >
                            {(vendor as any).custom_id}
                          </span>
                        </div>
                      )}
                      {vendor.specialization && (
                        <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-300">
                          <Briefcase className="w-3 h-3" />
                          <span className="truncate">{vendor.specialization}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleVendorStatus(vendor)}
                      className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
                      title={vendor.is_active ? 'Deactivate vendor' : 'Activate vendor'}
                    >
                      {vendor.is_active ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setEditingVendor(vendor)}
                      className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(vendor)}
                      disabled={deletingVendorId === vendor.id}
                      className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-900/20 dark:hover:text-red-400"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Contact Info */}
                <div className="space-y-2 mb-3">
                  {vendor.email && (
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                      <Mail className="w-3 h-3 flex-shrink-0" />
                      <span className="truncate">{vendor.email}</span>
                    </div>
                  )}
                  {vendor.phone && (
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                      <Phone className="w-3 h-3 flex-shrink-0" />
                      <span>{vendor.phone}</span>
                    </div>
                  )}
                  {vendor.contact_person && (
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                      <User className="w-3 h-3 flex-shrink-0" />
                      <span className="truncate">{vendor.contact_person}</span>
                    </div>
                  )}
                  {vendor.address && (
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                      <MapPin className="w-3 h-3 flex-shrink-0" />
                      <span className="truncate">{vendor.address}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Create Vendor Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Add New Vendor"
      >
        <VendorForm
          onSuccess={handleCreateSuccess}
          onCancel={() => setIsCreateModalOpen(false)}
        />
      </Modal>

      {/* Edit Vendor Modal */}
      <Modal
        isOpen={!!editingVendor}
        onClose={() => setEditingVendor(null)}
        title="Edit Vendor"
      >
        {editingVendor && (
          <VendorForm
            vendor={editingVendor}
            onSuccess={handleEditSuccess}
            onCancel={() => setEditingVendor(null)}
          />
        )}
      </Modal>
    </div>
  )
}
