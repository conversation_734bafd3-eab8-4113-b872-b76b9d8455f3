'use client'

import { useState, useEffect } from 'react'
import { AdminTaskManager } from '@/components/ui/admin-task-manager'
import { usersApi } from '@/lib/api'
import { createClientSupabaseClient } from '@/lib/auth'
import toast from 'react-hot-toast'

export default function AdminTasksPage() {
  const [currentUserRole, setCurrentUserRole] = useState<string>('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    getCurrentUserRole()
  }, [])

  const getCurrentUserRole = async () => {
    try {
      const supabase = createClientSupabaseClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        toast.error('Not authenticated')
        return
      }

      const { data: userProfile, error } = await supabase
        .from('users')
        .select('role')
        .eq('id', user.id)
        .single()

      if (error) {
        console.error('Error fetching user role:', error)
        toast.error('Failed to load user permissions')
        return
      }

      setCurrentUserRole(userProfile?.role || '')
    } catch (error: any) {
      console.error('Error getting current user role:', error)
      toast.error('Failed to load user permissions')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-8">
          <p className="text-gray-500">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <AdminTaskManager currentUserRole={currentUserRole} />
    </div>
  )
}
