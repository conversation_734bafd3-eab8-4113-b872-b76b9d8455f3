'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import {
  useProjects,
  useUpcomingSchedules,
  usePayments,
  useExpenses,
  useTasks,
  useClients
} from '@/hooks/useApi'
import { useRealTimeDashboard } from '@/hooks/useRealTimeDashboard'
import { formatCurrency, formatDate } from '@/lib/utils'
import { DashboardSkeleton } from '@/components/ui/skeleton'
import dynamic from 'next/dynamic'

// Lazy load heavy components
const DashboardAnalytics = dynamic(() => import('@/components/ui/dashboard-analytics').then(mod => ({ default: mod.DashboardAnalytics })), {
  loading: () => <div className="animate-pulse bg-muted rounded-lg h-64" />,
  ssr: false
})
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Users,
  MapPin,
  Calendar,
  Activity
} from 'lucide-react'

export default function DashboardPage() {
  // All hooks must be called before any conditional returns
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter'>('month')
  
  const { user } = useAuth()
  const { stats, loading: statsLoading } = useRealTimeDashboard()
  const { data: projects, loading: projectsLoading } = useProjects()
  const { data: upcomingSchedules, loading: schedulesLoading } = useUpcomingSchedules()
  const { data: payments, loading: paymentsLoading } = usePayments()
  const { data: expenses, loading: expensesLoading } = useExpenses()
  const { data: tasks, loading: tasksLoading } = useTasks()
  const { data: clients, loading: clientsLoading } = useClients()

  // Early return after all hooks are called
  if (!user) return null

  const loading = statsLoading || projectsLoading || schedulesLoading || paymentsLoading || expensesLoading || tasksLoading || clientsLoading

  if (loading) {
    return <DashboardSkeleton />
  }

  // Calculate real-time metrics first
  const totalRevenue = payments?.reduce((sum, p) => sum + p.amount, 0) || 0
  const totalExpenses = expenses?.reduce((sum, e) => sum + e.amount, 0) || 0
  const netIncome = totalRevenue - totalExpenses
  const pendingTasks = tasks?.filter(t => t.status === 'pending').length || 0
  const overdueTasks = tasks?.filter(t =>
    t.due_date && new Date(t.due_date) < new Date() && t.status !== 'completed'
  ).length || 0

  // Analytics data
  const analyticsData = {
    revenue: {
      total: totalRevenue,
      monthly: payments?.filter(p => {
        const paymentDate = new Date(p.payment_date)
        const now = new Date()
        return paymentDate.getMonth() === now.getMonth() && paymentDate.getFullYear() === now.getFullYear()
      }).reduce((sum, p) => sum + p.amount, 0) || 0,
      growth: 15.2 // Mock growth percentage
    },
    projects: {
      total: projects?.length || 0,
      active: projects?.filter(p => p.status === 'active').length || 0,
      completed: projects?.filter(p => p.status === 'completed').length || 0,
      completionRate: projects?.length ? Math.round((projects.filter(p => p.status === 'completed').length / projects.length) * 100) : 0
    },
    clients: {
      total: clients?.length || 0,
      active: clients?.length || 0,
      retention: 85 // Mock retention rate
    },
    performance: {
      avgProjectValue: projects?.length ? Math.round(totalRevenue / projects.length) : 0,
      avgCompletionTime: 12, // Mock average completion time in days
      efficiency: 92 // Mock efficiency percentage
    }
  }

  // Recent activity
  const recentActivity = [
    ...(projects?.slice(0, 2).map(p => ({
      type: 'project',
      message: `New project "${p.name}" was created`,
      time: p.created_at,
      color: 'green',
      icon: 'building'
    })) || []),
    ...(payments?.slice(0, 2).map(p => ({
      type: 'payment',
      message: `Payment received ₹${p.amount.toLocaleString()}`,
      time: p.created_at,
      color: 'blue',
      icon: 'dollar'
    })) || []),
    ...(tasks?.filter(t => t.status === 'completed').slice(0, 2).map(t => ({
      type: 'task',
      message: `Task "${t.title}" completed`,
      time: t.updated_at || t.created_at,
      color: 'purple',
      icon: 'check'
    })) || [])
  ].sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime()).slice(0, 5)

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="app-card overflow-hidden">
        <div className="bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 border-b border-gray-200 dark:border-gray-700 p-8">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg">
              <Activity className="w-8 h-8 text-white" />
            </div>
            <div>
              <h2 className="text-3xl font-bold mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Welcome back, {user.name}!
              </h2>
              <p className="text-muted-foreground text-lg">
                Here's what's happening with your drone operations today.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-flow-col auto-cols-[minmax(260px,1fr)] md:grid-flow-row md:auto-cols-auto md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 overflow-x-auto no-scrollbar snap-x snap-mandatory -mx-4 px-4">
        {/* Active Projects Card */}
        <div className="app-card overflow-hidden group hover:scale-[1.01] transition-all duration-300 snap-start min-w-[260px]">
          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border-b border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center justify-between">
              <div className="p-3 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <TrendingUp className="w-5 h-5 text-green-500" />
            </div>
          </div>
          <div className="p-4">
            <p className="text-sm font-medium text-muted-foreground mb-1">Active Projects</p>
            <p className="text-3xl font-bold text-foreground">{stats?.activeProjects || 0}</p>
            <p className="text-xs text-green-600 dark:text-green-400 mt-1">+12% from last month</p>
          </div>
        </div>

        {/* Upcoming Schedules Card */}
        <div className="app-card overflow-hidden group hover:scale-[1.01] transition-all duration-300 snap-start min-w-[260px]">
          <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 border-b border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center justify-between">
              <div className="p-3 bg-gradient-to-r from-green-600 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow">
                <Calendar className="w-6 h-6 text-white" />
              </div>
              <Clock className="w-5 h-5 text-blue-500" />
            </div>
          </div>
          <div className="p-4">
            <p className="text-sm font-medium text-muted-foreground mb-1">Upcoming Schedules</p>
            <p className="text-3xl font-bold text-foreground">{stats?.upcomingSchedules || 0}</p>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">Next in 2 days</p>
          </div>
        </div>

        {/* Overdue Tasks Card */}
        <div className="app-card overflow-hidden group hover:scale-[1.01] transition-all duration-300 snap-start min-w-[260px]">
          <div className="bg-gradient-to-r from-orange-500/10 to-red-500/10 border-b border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center justify-between">
              <div className="p-3 bg-gradient-to-r from-orange-600 to-red-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow">
                <AlertTriangle className="w-6 h-6 text-white" />
              </div>
              {(stats?.overdueTasks || 0) > 0 && <TrendingDown className="w-5 h-5 text-red-500" />}
            </div>
          </div>
          <div className="p-4">
            <p className="text-sm font-medium text-muted-foreground mb-1">Overdue Tasks</p>
            <p className="text-3xl font-bold text-foreground">{stats?.overdueTasks || 0}</p>
            <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">Needs attention</p>
          </div>
        </div>

        {/* Revenue Card */}
        <div className="app-card overflow-hidden group hover:scale-[1.01] transition-all duration-300 snap-start min-w-[260px]">
          <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border-b border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center justify-between">
              <div className="p-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow">
                <DollarSign className="w-6 h-6 text-white" />
              </div>
              <TrendingUp className="w-5 h-5 text-green-500" />
            </div>
          </div>
          <div className="p-4">
            <p className="text-sm font-medium text-muted-foreground mb-1">Revenue (Month)</p>
            <p className="text-3xl font-bold text-foreground">{formatCurrency(stats?.monthlyRevenue || 0)}</p>
            <p className="text-xs text-green-600 dark:text-green-400 mt-1">+8.2% from last month</p>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Projects Card */}
        <div className="app-card overflow-hidden">
          <div className="bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border-b border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow-lg">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground">Recent Projects</h3>
                <p className="text-sm text-muted-foreground">Latest project activities</p>
              </div>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {projects && projects.length > 0 ? (
                projects.slice(0, 3).map((project, index) => (
                  <div key={project.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div>
                        <p className="font-medium text-foreground">{project.name}</p>
                        <p className="text-sm text-muted-foreground flex items-center">
                          <Users className="w-3 h-3 mr-1" />
                          {project.client?.name}
                        </p>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      project.status === 'active' ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-700 dark:text-green-400 border border-green-500/30' :
                      project.status === 'completed' ? 'bg-gradient-to-r from-blue-500/20 to-indigo-500/20 text-blue-700 dark:text-blue-400 border border-blue-500/30' :
                      project.status === 'on_hold' ? 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-700 dark:text-yellow-400 border border-yellow-500/30' :
                      'bg-gradient-to-r from-red-500/20 to-pink-500/20 text-red-700 dark:text-red-400 border border-red-500/30'
                    }`}>
                      {project.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-full w-fit mx-auto mb-3">
                    <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <p className="text-muted-foreground">No projects found</p>
                </div>
              )}
            </div>
            <div className="mt-6">
              <Button variant="outline" className="w-full btn-gradient-blue">
                View All Projects
              </Button>
            </div>
          </div>
        </div>

        {/* Upcoming Schedules Card */}
        <div className="app-card overflow-hidden">
          <div className="bg-gradient-to-r from-green-500/10 to-teal-500/10 border-b border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-green-600 to-teal-600 rounded-lg shadow-lg">
                <Calendar className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground">Upcoming Schedules</h3>
                <p className="text-sm text-muted-foreground">Scheduled drone operations</p>
              </div>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {upcomingSchedules && upcomingSchedules.length > 0 ? (
                upcomingSchedules.slice(0, 3).map((schedule, index) => (
                  <div key={schedule.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <div>
                        <p className="font-medium text-foreground">{schedule.project?.name}</p>
                        <p className="text-sm text-muted-foreground flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {new Date(schedule.scheduled_date).toLocaleDateString('en-AU', {
                            weekday: 'short',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="w-3 h-3 mr-1" />
                        <span className="truncate max-w-24">{schedule.project?.location || 'TBD'}</span>
                      </div>
                      <div className="text-xs text-green-600 dark:text-green-400 mt-1">
                        {Math.ceil((new Date(schedule.scheduled_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-full w-fit mx-auto mb-3">
                    <Calendar className="w-8 h-8 text-green-400" />
                  </div>
                  <p className="text-muted-foreground">No upcoming schedules</p>
                </div>
              )}
            </div>
            <div className="mt-6">
              <Button variant="outline" className="w-full btn-gradient-green">
                View Schedule
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Financial Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-foreground">Financial Overview</h3>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">This Month</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-green-100 dark:bg-green-950/50 border border-green-300 dark:border-green-800/30 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-400" />
                <span className="text-sm font-medium text-green-900 dark:text-green-300">Total Revenue</span>
              </div>
              <div className="text-2xl font-bold text-green-900 dark:text-white">₹{totalRevenue.toLocaleString()}</div>
              <div className="text-sm text-green-800 dark:text-green-400">From {payments?.length || 0} payments</div>
            </div>

            <div className="bg-red-100 dark:bg-red-950/50 border border-red-300 dark:border-red-800/30 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingDown className="w-5 h-5 text-red-600 dark:text-red-400" />
                <span className="text-sm font-medium text-red-900 dark:text-red-300">Total Expenses</span>
              </div>
              <div className="text-2xl font-bold text-red-900 dark:text-white">₹{totalExpenses.toLocaleString()}</div>
              <div className="text-sm text-red-800 dark:text-red-400">From {expenses?.length || 0} expenses</div>
            </div>

            <div className={`border rounded-lg p-4 ${
              netIncome >= 0
                ? 'bg-blue-100 dark:bg-blue-950/50 border-blue-300 dark:border-blue-800/30'
                : 'bg-orange-100 dark:bg-orange-950/50 border-orange-300 dark:border-orange-800/30'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                <DollarSign className={`w-5 h-5 ${
                  netIncome >= 0 ? 'text-blue-600 dark:text-blue-400' : 'text-orange-600 dark:text-orange-400'
                }`} />
                <span className={`text-sm font-medium ${
                  netIncome >= 0 ? 'text-blue-900 dark:text-blue-300' : 'text-orange-900 dark:text-orange-300'
                }`}>Net Income</span>
              </div>
              <div className={`text-2xl font-bold ${
                netIncome >= 0 ? 'text-blue-900 dark:text-white' : 'text-orange-900 dark:text-white'
              }`}>₹{netIncome.toLocaleString()}</div>
              <div className={`text-sm ${
                netIncome >= 0 ? 'text-blue-800 dark:text-blue-400' : 'text-orange-800 dark:text-orange-400'
              }`}>
                {netIncome >= 0 ? 'Profit' : 'Loss'} this period
              </div>
            </div>
          </div>

          {/* Recent Transactions */}
          <div>
            <h4 className="font-medium text-foreground mb-3">Recent Transactions</h4>
            <div className="space-y-2">
              {[
                ...(payments?.slice(0, 3).map(p => ({
                  type: 'payment',
                  amount: p.amount,
                  description: `Payment from ${p.project?.client?.name}`,
                  date: p.payment_date,
                  positive: true
                })) || []),
                ...(expenses?.slice(0, 2).map(e => ({
                  type: 'expense',
                  amount: e.amount,
                  description: e.description,
                  date: e.date,
                  positive: false
                })) || [])
              ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 5).map((transaction, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b border-border last:border-b-0">
                  <div className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${
                      transaction.positive ? 'bg-green-500' : 'bg-red-500'
                    }`}></div>
                    <div>
                      <p className="text-sm font-medium text-foreground">{transaction.description}</p>
                      <p className="text-xs text-muted-foreground">{formatDate(transaction.date)}</p>
                    </div>
                  </div>
                  <span className={`text-sm font-medium ${
                    transaction.positive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {transaction.positive ? '+' : '-'}₹{transaction.amount.toLocaleString()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Task Overview */}
        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <h3 className="text-lg font-medium text-foreground mb-4">Task Overview</h3>

          <div className="space-y-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Pending Tasks</span>
              </div>
              <span className="text-lg font-bold text-foreground">{pendingTasks}</span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-4 h-4 text-red-600 dark:text-red-400" />
                <span className="text-sm text-muted-foreground">Overdue Tasks</span>
              </div>
              <span className="text-lg font-bold text-red-600 dark:text-red-400">{overdueTasks}</span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                <span className="text-sm text-muted-foreground">Completed Today</span>
              </div>
              <span className="text-lg font-bold text-green-600 dark:text-green-400">
                {tasks?.filter(t =>
                  t.status === 'completed' &&
                  new Date(t.updated_at || t.created_at).toDateString() === new Date().toDateString()
                ).length || 0}
              </span>
            </div>
          </div>

          {/* Urgent Tasks */}
          <div>
            <h4 className="font-medium text-foreground mb-3">Urgent Tasks</h4>
            <div className="space-y-2">
              {tasks?.filter(t => t.priority === 'urgent' && t.status !== 'completed').slice(0, 3).map((task) => (
                <div key={task.id} className="flex items-center space-x-2 p-2 bg-red-100 dark:bg-red-950/50 border border-red-300 dark:border-red-800/30 rounded">
                  <AlertTriangle className="w-4 h-4 text-red-600 dark:text-red-400 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-red-900 dark:text-white truncate">{task.title}</p>
                    {task.due_date && (
                      <p className="text-xs text-red-800 dark:text-red-400">Due: {formatDate(task.due_date)}</p>
                    )}
                  </div>
                </div>
              )) || (
                <p className="text-sm text-muted-foreground text-center py-2">No urgent tasks</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-card rounded-lg shadow p-6 border border-border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-foreground">Recent Activity</h3>
          <Activity className="w-5 h-5 text-muted-foreground" />
        </div>
        <div className="space-y-3">
          {recentActivity.length > 0 ? recentActivity.map((activity, index) => (
            <div key={index} className="flex items-center space-x-3">
              <div className={`w-2 h-2 rounded-full ${
                activity.color === 'green' ? 'bg-green-500' :
                activity.color === 'blue' ? 'bg-blue-500' :
                activity.color === 'purple' ? 'bg-purple-500' :
                'bg-gray-500'
              }`}></div>
              <div className="flex-1">
                <p className="text-sm text-foreground">{activity.message}</p>
                <p className="text-xs text-muted-foreground">{formatDate(activity.time)}</p>
              </div>
            </div>
          )) : (
            <p className="text-muted-foreground text-center py-4">No recent activity</p>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-card rounded-lg shadow p-6 border border-border">
        <h3 className="text-lg font-medium text-foreground mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button className="h-auto p-4 flex flex-col items-center space-y-2">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span>New Project</span>
          </Button>
          <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span>Schedule Session</span>
          </Button>
          <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <span>Add Client</span>
          </Button>
          <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <span>View Reports</span>
          </Button>
        </div>
      </div>

      {/* Advanced Analytics */}
      <DashboardAnalytics
        data={analyticsData}
        timeRange={timeRange}
        onTimeRangeChange={setTimeRange}
      />
    </div>
  )
}
