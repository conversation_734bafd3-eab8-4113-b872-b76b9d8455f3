'use client'

import { useState, useEffect } from 'react'
import {
  format,
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  addDays,
  isSameMonth,
  isSameDay,
  isToday,
  addMonths,
  subMonths,
  parseISO
} from 'date-fns'
import { shootsApi } from '@/lib/api'
import { Shoot } from '@/types'
import { Button } from '@/components/ui/button'
import {
  Calendar as CalendarIcon,
  MapPin,
  User,
  Building2,
  Clock,
  Plus,
  Filter,
  ChevronLeft,
  ChevronRight,
  Eye,
  Edit
} from 'lucide-react'
import toast from 'react-hot-toast'

// Status color mapping
const getStatusColor = (status: string) => {
  switch (status) {
    case 'scheduled':
      return 'bg-blue-500 border-blue-600 text-white'
    case 'completed':
      return 'bg-green-500 border-green-600 text-white'
    case 'cancelled':
      return 'bg-red-500 border-red-600 text-white'
    case 'rescheduled':
      return 'bg-yellow-500 border-yellow-600 text-white'
    default:
      return 'bg-gray-500 border-gray-600 text-white'
  }
}

// Get shoots for a specific date
const getShootsForDate = (shoots: Shoot[], date: Date) => {
  return shoots.filter(shoot => {
    const shootDate = parseISO(shoot.scheduled_date)
    return isSameDay(shootDate, date)
  })
}

export default function CalendarPage() {
  const [shoots, setShoots] = useState<Shoot[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedShoot, setSelectedShoot] = useState<Shoot | null>(null)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)
  const [isAddShootModalOpen, setIsAddShootModalOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [currentDate, setCurrentDate] = useState(new Date())

  // Fetch shoots data
  const fetchShoots = async () => {
    try {
      setLoading(true)
      const shootsData = await shootsApi.getAll()
      setShoots(shootsData)
    } catch (error: any) {
      toast.error('Failed to load shoots')
      console.error('Error fetching shoots:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchShoots()
  }, [])

  // Handle shoot selection
  const handleSelectShoot = (shoot: Shoot) => {
    setSelectedShoot(shoot)
    setIsDetailsModalOpen(true)
  }

  // Handle date selection (for adding new shoots)
  const handleSelectDate = (date: Date) => {
    setSelectedDate(date)
    setIsAddShootModalOpen(true)
  }

  // Generate calendar days
  const generateCalendarDays = () => {
    const monthStart = startOfMonth(currentDate)
    const monthEnd = endOfMonth(currentDate)
    const startDate = startOfWeek(monthStart)
    const endDate = endOfWeek(monthEnd)

    const days = []
    let day = startDate

    while (day <= endDate) {
      days.push(day)
      day = addDays(day, 1)
    }

    return days
  }

  const calendarDays = generateCalendarDays()

  // Navigation handlers
  const goToPreviousMonth = () => setCurrentDate(subMonths(currentDate, 1))
  const goToNextMonth = () => setCurrentDate(addMonths(currentDate, 1))
  const goToToday = () => setCurrentDate(new Date())

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading calendar...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-full">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-foreground">Calendar</h1>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPreviousMonth}
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={goToToday}
            >
              Today
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={goToNextMonth}
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
          <h2 className="text-lg font-semibold text-foreground">
            {format(currentDate, 'MMMM yyyy')}
          </h2>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            onClick={() => setIsAddShootModalOpen(true)}
            size="sm"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Shoot
          </Button>
        </div>
      </div>

      {/* Main Content Layout */}
      <div className="flex flex-col xl:flex-row gap-6">
        {/* Calendar Section */}
        <div className="flex-1">
          {/* Calendar Grid */}
          <div className="bg-card rounded-lg border border-border overflow-hidden">
            {/* Days of week header */}
            <div className="grid grid-cols-7 bg-muted">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <div key={day} className="p-4 text-center font-medium text-muted-foreground border-r border-border last:border-r-0">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar days */}
            <div className="grid grid-cols-7">
              {calendarDays.map((day, index) => {
                const dayShots = getShootsForDate(shoots, day)
                const isCurrentMonth = isSameMonth(day, currentDate)
                const isCurrentDay = isToday(day)

                return (
                  <div
                    key={index}
                    className={`min-h-[120px] p-2 border-r border-b border-border last:border-r-0 cursor-pointer hover:bg-muted/50 transition-colors ${
                      !isCurrentMonth ? 'bg-muted/20 text-muted-foreground' : ''
                    } ${isCurrentDay ? 'bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-700' : ''}`}
                    onClick={() => handleSelectDate(day)}
                  >
                    <div className={`text-sm font-medium mb-2 ${
                      isCurrentDay
                        ? 'text-blue-700 dark:text-blue-300 bg-blue-200 dark:bg-blue-800 px-2 py-1 rounded-full text-center w-6 h-6 flex items-center justify-center'
                        : ''
                    }`}>
                      {format(day, 'd')}
                    </div>

                    {/* Shoots for this day */}
                    <div className="space-y-1">
                      {dayShots.slice(0, 3).map((shoot) => {
                        const statusColor = getStatusColor(shoot.status)
                        return (
                          <div
                            key={shoot.id}
                            className={`p-1 rounded text-xs ${statusColor} cursor-pointer hover:opacity-80 transition-opacity`}
                            onClick={(e) => {
                              e.stopPropagation()
                              handleSelectShoot(shoot)
                            }}
                          >
                            <div className="font-medium truncate">{shoot.project?.name}</div>
                            <div className="truncate opacity-90">{shoot.project?.client?.name}</div>
                            {shoot.location && (
                              <div className="flex items-center mt-1 opacity-75">
                                <MapPin className="w-3 h-3 mr-1" />
                                <span className="truncate">{shoot.location}</span>
                              </div>
                            )}
                          </div>
                        )
                      })}
                      {dayShots.length > 3 && (
                        <div className="text-xs text-muted-foreground">
                          +{dayShots.length - 3} more
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Upcoming Shoots Sidebar - Right on Desktop, Bottom on Mobile/Tablet */}
        <div className="xl:w-80 xl:flex-shrink-0">
          <div className="bg-card rounded-lg border border-border p-4 xl:sticky xl:top-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-foreground">Next 10 Days</h3>
              <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                {(() => {
                  const today = new Date()
                  const tenDaysFromNow = new Date(today.getTime() + 10 * 24 * 60 * 60 * 1000)
                  return shoots.filter(shoot => {
                    const shootDate = new Date(shoot.scheduled_date)
                    return shootDate >= today &&
                           shootDate <= tenDaysFromNow &&
                           shoot.status === 'scheduled'
                  }).length
                })()} shoots
              </div>
            </div>

            {/* Mobile/Tablet: Horizontal scroll, Desktop: Vertical scroll */}
            <div className="xl:space-y-3 xl:max-h-[calc(100vh-200px)] xl:overflow-y-auto flex xl:flex-col gap-3 xl:gap-0 overflow-x-auto xl:overflow-x-visible pb-2 xl:pb-0">
              {(() => {
                const today = new Date()
                const tenDaysFromNow = new Date(today.getTime() + 10 * 24 * 60 * 60 * 1000)
                return shoots
                  .filter(shoot => {
                    const shootDate = new Date(shoot.scheduled_date)
                    return shootDate >= today &&
                           shootDate <= tenDaysFromNow &&
                           shoot.status === 'scheduled'
                  })
                  .sort((a, b) => new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime())
              })()
                .map((shoot) => (
                  <div
                    key={shoot.id}
                    className="p-3 bg-muted/50 rounded-lg border border-border hover:bg-muted transition-colors cursor-pointer xl:w-auto w-64 flex-shrink-0"
                    onClick={() => handleSelectShoot(shoot)}
                  >
                    {/* Date */}
                    <div className="flex items-center text-xs text-muted-foreground mb-2">
                      <CalendarIcon className="w-3 h-3 mr-1" />
                      <span>{format(parseISO(shoot.scheduled_date), 'MMM dd, yyyy')}</span>
                      <span className="ml-2">{format(parseISO(shoot.scheduled_date), 'h:mm a')}</span>
                    </div>

                    {/* Project Info */}
                    <div className="mb-2">
                      <div className="font-medium text-sm text-foreground truncate">
                        {shoot.project?.name}
                      </div>
                      <div className="text-xs text-muted-foreground truncate">
                        {shoot.project?.client?.name}
                      </div>
                    </div>

                    {/* Location */}
                    {shoot.location && (
                      <div className="flex items-center text-xs text-muted-foreground mb-2">
                        <MapPin className="w-3 h-3 mr-1" />
                        <span className="truncate">{shoot.location}</span>
                      </div>
                    )}

                    {/* Amount */}
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium text-green-600 dark:text-green-400">
                        ₹{shoot.amount.toLocaleString()}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded ${getStatusColor(shoot.status)}`}>
                        {shoot.status}
                      </span>
                    </div>

                    {/* Days until shoot */}
                    <div className="mt-2 text-xs text-blue-600 dark:text-blue-400">
                      {(() => {
                        const daysUntil = Math.ceil((new Date(shoot.scheduled_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
                        if (daysUntil === 0) return 'Today'
                        if (daysUntil === 1) return 'Tomorrow'
                        return `In ${daysUntil} days`
                      })()}
                    </div>
                  </div>
                ))}

              {(() => {
                const today = new Date()
                const tenDaysFromNow = new Date(today.getTime() + 10 * 24 * 60 * 60 * 1000)
                const upcomingShootsCount = shoots.filter(shoot => {
                  const shootDate = new Date(shoot.scheduled_date)
                  return shootDate >= today &&
                         shootDate <= tenDaysFromNow &&
                         shoot.status === 'scheduled'
                }).length

                return upcomingShootsCount === 0 && (
                  <div className="text-center py-8 text-muted-foreground xl:w-auto w-64 flex-shrink-0">
                    <CalendarIcon className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p className="text-sm">No shoots in next 10 days</p>
                    <p className="text-xs mt-1">Schedule a new shoot to see it here</p>
                  </div>
                )
              })()}
            </div>
          </div>
        </div>
      </div>

      {/* Shoot Details Modal */}
      {selectedShoot && isDetailsModalOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Shoot Details</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setIsDetailsModalOpen(false)
                  setSelectedShoot(null)
                }}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                ×
              </Button>
            </div>

            {/* Shoot Details Content */}
            <div className="space-y-6">
              {/* Project Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-3">Project Information</h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center">
                      <Building2 className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                      <span className="font-medium text-gray-900 dark:text-white">{selectedShoot.project?.name}</span>
                    </div>
                    <div className="flex items-center">
                      <User className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                      <span className="text-gray-700 dark:text-gray-300">{selectedShoot.project?.client?.name}</span>
                    </div>
                    {selectedShoot.location && (
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                        <span className="text-gray-700 dark:text-gray-300">{selectedShoot.location}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-3">Shoot Details</h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center">
                      <CalendarIcon className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                      <span className="text-gray-700 dark:text-gray-300">{format(parseISO(selectedShoot.scheduled_date), 'PPP p')}</span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
                      <span className={`px-2 py-1 rounded text-xs ${getStatusColor(selectedShoot.status)}`}>
                        {selectedShoot.status}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-900 dark:text-white">Amount: ₹{selectedShoot.amount.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {selectedShoot.notes && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-3">Notes</h3>
                  <p className="text-sm text-gray-700 dark:text-gray-300">{selectedShoot.notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Add Shoot Modal */}
      {isAddShootModalOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Add New Shoot</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setIsAddShootModalOpen(false)
                  setSelectedDate(null)
                }}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                ×
              </Button>
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-4 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
              {selectedDate && `Selected date: ${format(selectedDate, 'PPP')}`}
            </div>
            {/* Add shoot form would go here - for now just close button */}
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setIsAddShootModalOpen(false)
                  setSelectedDate(null)
                }}
                className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  setIsAddShootModalOpen(false)
                  setSelectedDate(null)
                  toast.success('Shoot form would open here')
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Add Shoot
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
