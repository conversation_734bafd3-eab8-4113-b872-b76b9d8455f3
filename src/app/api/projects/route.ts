import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Server-side project creation started:', body)

    // Create Supabase client with service role key for server-side operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Insert the project into the database
    const { data, error } = await supabase
      .from('projects')
      .insert({
        ...body,
        total_amount: 0, // Will be calculated from shoots
        gst_inclusive: false, // Will be determined by client GST status
        amount_received: 0,
        amount_pending: 0
      })
      .select()
      .single()

    if (error) {
      console.error('Database error during project creation:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    console.log('Project created in database:', data)

    // Queue SharePoint folder creation as background job
    try {
      const { queueSharePointFolderCreation } = await import('@/lib/background-jobs')
      
      const jobId = queueSharePointFolderCreation('project', data.id, {
        customId: data.custom_id,
        name: data.name,
        clientId: data.client_id
      })
      
      console.log('SharePoint folder creation queued for project:', {
        projectId: data.id,
        customId: data.custom_id,
        name: data.name,
        clientId: data.client_id,
        jobId
      })
      
      // Add job ID to response for tracking
      ;(data as any).sharepoint_job_id = jobId
    } catch (jobError) {
      console.error('Failed to queue SharePoint folder creation for project:', {
        projectId: data.id,
        customId: data.custom_id,
        name: data.name,
        clientId: data.client_id,
        error: jobError instanceof Error ? jobError.message : jobError
      })
      // Don't fail the project creation if job queuing fails
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in project creation API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create project' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    const { data, error } = await supabase
      .from('projects')
      .select(`
        id, custom_id, name, description, client_id, location, google_maps_link, status, total_amount, gst_inclusive, amount_received, amount_pending, vendor_payment_status, vendor_payment_amount, vendor_payment_due_date, vendor_payment_date, vendor_payment_notes, created_at, updated_at,
        client:clients(name)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Database error during projects fetch:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in projects GET API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch projects' },
      { status: 500 }
    )
  }
}