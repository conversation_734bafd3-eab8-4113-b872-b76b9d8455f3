import { NextRequest, NextResponse } from 'next/server'
import { SharePointService } from '@/lib/sharepoint-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { scheduleId } = body

    if (!scheduleId) {
      return NextResponse.json({
        success: false,
        message: 'Schedule ID is required'
      }, { status: 400 })
    }

    console.log('🚀 Creating SharePoint folder for schedule:', scheduleId)
    
    const success = await SharePointService.ensureScheduleFolder(scheduleId)
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: `SharePoint folder created successfully for schedule ${scheduleId}`
      })
    } else {
      return NextResponse.json({
        success: false,
        message: `Failed to create SharePoint folder for schedule ${scheduleId}`
      }, { status: 500 })
    }
  } catch (error) {
    console.error('❌ Failed to create SharePoint folder:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to create SharePoint folder',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST with scheduleId to create SharePoint folder for a specific schedule'
  })
}