import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.error('Unauthorized delete attempt:', authError)
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const taskId = params.id
    console.log('🗑️ Deleting task:', taskId, 'by user:', user.email)

    // Check if user has permission to delete (admin or manager)
    if (user.user_metadata?.role !== 'admin' && user.user_metadata?.role !== 'manager') {
      console.error('Insufficient permissions for delete:', user.user_metadata?.role)
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Check if task exists and get its details
    const { data: task, error: fetchError } = await supabase
      .from('tasks')
      .select('id, title, project_id, shoot_id')
      .eq('id', taskId)
      .single()

    if (fetchError) {
      console.error('Error fetching task for deletion:', fetchError)
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      )
    }

    console.log('Task to delete:', task)

    // Delete the task
    const { error: deleteError } = await supabase
      .from('tasks')
      .delete()
      .eq('id', taskId)

    if (deleteError) {
      console.error('❌ Error deleting task:', deleteError)
      return NextResponse.json(
        { error: deleteError.message },
        { status: 500 }
      )
    }

    console.log('✅ Task deleted successfully:', task.title)

    return NextResponse.json({ 
      success: true,
      message: `Task "${task.title}" deleted successfully`
    })

  } catch (error) {
    console.error('❌ Error in task delete API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete task' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const taskId = params.id

    const { data: task, error } = await supabase
      .from('tasks')
      .select(`
        id, title, description, status, priority, due_date, assigned_to, project_id, shoot_id, comments, created_at, updated_at,
        assignee:users(id, name, email, role),
        project:projects(id, custom_id, name),
        shoot:schedules(id, custom_id, scheduled_date)
      `)
      .eq('id', taskId)
      .single()

    if (error) {
      console.error('Error fetching task:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!task) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 })
    }

    return NextResponse.json(task)

  } catch (error) {
    console.error('Error in task GET API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch task' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const taskId = params.id
    const updates = await request.json()

    console.log('🔄 Updating task:', taskId, 'with:', Object.keys(updates))

    // First, get the current task to check if it's a File Upload task
    const { data: currentTask, error: fetchError } = await supabase
      .from('tasks')
      .select('id, title, status, shoot_id')
      .eq('id', taskId)
      .single()

    if (fetchError) {
      console.error('❌ Error fetching current task:', fetchError)
      return NextResponse.json({ error: fetchError.message }, { status: 500 })
    }

    const { data: task, error } = await supabase
      .from('tasks')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId)
      .select(`
        id, title, description, status, priority, due_date, assigned_to, project_id, shoot_id, comments, created_at, updated_at,
        assignee:users(id, name, email, role),
        project:projects(id, custom_id, name),
        shoot:schedules(id, custom_id, scheduled_date)
      `)
      .single()

    if (error) {
      console.error('❌ Error updating task:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    console.log('✅ Task updated successfully:', task.title)

    // Check if this is a File Upload task being marked as completed
    const isFileUploadTask = currentTask.title?.toLowerCase().includes('file upload') || 
                            currentTask.title?.toLowerCase().includes('upload')
    const isBeingCompleted = updates.status === 'completed' && currentTask.status !== 'completed'
    
    if (isFileUploadTask && isBeingCompleted && currentTask.shoot_id) {
      console.log('🗺️ File Upload task completed, triggering heatmap automation for schedule:', currentTask.shoot_id)
      
      try {
          // Queue heatmap automation as a background job
          const { queueBackgroundJob } = await import('@/lib/background-jobs')
          const jobId = queueBackgroundJob('heatmap_automation', 'schedule', currentTask.shoot_id, { scheduleId: currentTask.shoot_id })
          console.log('✅ Heatmap automation queued with job ID:', jobId)
        } catch (automationError) {
        console.error('⚠️ Failed to queue heatmap automation:', automationError)
        // Don't fail the task update if automation fails
      }
    }

    return NextResponse.json(task)

  } catch (error) {
    console.error('❌ Error in task update API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update task' },
      { status: 500 }
    )
  }
}
