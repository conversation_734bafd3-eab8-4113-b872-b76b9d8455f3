import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'
import type { CreateTaskForm } from '@/types'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.error('Unauthorized task creation attempt:', authError)
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const taskData: CreateTaskForm = await request.json()

    console.log('🔄 Creating task via API endpoint:', {
      title: taskData.title,
      project_id: taskData.project_id,
      shoot_id: taskData.shoot_id,
      assigned_to: taskData.assigned_to,
      user_id: user.id
    })

    // Validate required fields
    if (!taskData.title || !taskData.project_id) {
      return NextResponse.json(
        { error: 'Title and project_id are required' },
        { status: 400 }
      )
    }

    // Set defaults
    const taskToCreate = {
      ...taskData,
      status: taskData.status || 'pending',
      priority: taskData.priority || 'medium',
      assigned_to: taskData.assigned_to || user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // Create the task
    const { data: task, error: createError } = await supabase
      .from('tasks')
      .insert(taskToCreate)
      .select(`
        id, title, description, status, priority, due_date, assigned_to, project_id, shoot_id, comments, created_at, updated_at,
        assignee:users(id, name, email, role),
        project:projects(id, custom_id, name),
        shoot:schedules(id, custom_id, scheduled_date)
      `)
      .single()

    if (createError) {
      console.error('❌ Error creating task:', createError)
      return NextResponse.json(
        { error: createError.message },
        { status: 500 }
      )
    }

    console.log('✅ Task created successfully:', task.title, task.id)

    return NextResponse.json(task, { status: 201 })

  } catch (error) {
    console.error('❌ Error in task creation API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create task' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('project_id')
    const shootId = searchParams.get('shoot_id')
    const status = searchParams.get('status')
    const assignedTo = searchParams.get('assigned_to')

    let query = supabase
      .from('tasks')
      .select(`
        id, title, description, status, priority, due_date, assigned_to, project_id, shoot_id, comments, created_at, updated_at,
        assignee:users(id, name, email, role),
        project:projects(id, custom_id, name),
        shoot:schedules(id, custom_id, scheduled_date)
      `)

    // Apply filters
    if (projectId) {
      query = query.eq('project_id', projectId)
    }
    if (shootId) {
      query = query.eq('shoot_id', shootId)
    }
    if (status) {
      query = query.eq('status', status)
    }
    if (assignedTo) {
      query = query.eq('assigned_to', assignedTo)
    }

    const { data: tasks, error } = await query
      .order('order', { ascending: true, nullsFirst: false })
      .order('created_at', { ascending: true })

    if (error) {
      console.error('Error fetching tasks:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(tasks || [])

  } catch (error) {
    console.error('Error in tasks GET API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch tasks' },
      { status: 500 }
    )
  }
}
