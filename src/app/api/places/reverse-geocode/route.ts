import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const lat = searchParams.get('lat')
    const lng = searchParams.get('lng')

    console.log('Reverse geocode request:', { lat, lng })

    if (!lat || !lng) {
      return NextResponse.json(
        { error: 'lat and lng parameters are required' },
        { status: 400 }
      )
    }

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
    if (!apiKey) {
      console.error('Google Maps API key not found in environment variables')
      return NextResponse.json(
        { error: 'Google Maps API key not configured' },
        { status: 500 }
      )
    }

    // Try the new Places API (Nearby Search) first
    const newApiUrl = `https://places.googleapis.com/v1/places:searchNearby`

    console.log('Making request to Google Places Nearby API (New)')

    try {
      const newApiResponse = await fetch(newApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Goog-Api-Key': apiKey,
          'X-Goog-FieldMask': 'places.id,places.displayName,places.formattedAddress,places.location'
        },
        body: JSON.stringify({
          locationRestriction: {
            circle: {
              center: {
                latitude: parseFloat(lat),
                longitude: parseFloat(lng)
              },
              radius: 100.0 // Increased radius to 100 meters
            }
          },
          maxResultCount: 1
        })
      })

      if (newApiResponse.ok) {
        const newData = await newApiResponse.json()
        console.log('New Places Nearby API response:', { places_count: newData.places?.length || 0 })

        if (newData.places && newData.places.length > 0) {
          const place = newData.places[0]
          return NextResponse.json({
            formattedLocation: place.formattedAddress || place.displayName?.text || 'Current Location',
            placeName: place.displayName?.text || '',
            coordinates: {
              lat: parseFloat(lat),
              lng: parseFloat(lng)
            }
          })
        }
      } else {
        console.log('New Places API failed with status:', newApiResponse.status)
      }
    } catch (error) {
      console.error('New Places API error:', error)
    }

    // Fallback to legacy Geocoding API
    const legacyApiUrl = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`
    
    console.log('Falling back to legacy Geocoding API')

    const response = await fetch(legacyApiUrl)

    console.log('Google Geocoding API response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Geocoding API HTTP error:', response.status, errorText)
      return NextResponse.json(
        { error: `Geocoding API HTTP error: ${response.status}` },
        { status: response.status }
      )
    }

    const data = await response.json()
    console.log('Google Geocoding API response:', { status: data.status, results_count: data.results?.length || 0 })

    if (data.status === 'OK' && data.results && data.results.length > 0) {
      const result = data.results[0]
      
      return NextResponse.json({
        formattedLocation: result.formatted_address,
        placeName: result.address_components?.[0]?.long_name || '',
        coordinates: {
          lat: parseFloat(lat),
          lng: parseFloat(lng)
        }
      })
    } else {
      console.error('Geocoding API error:', data.status, data.error_message)

      // Fallback: return coordinates as location
      console.log('Falling back to coordinate-based location')
      return NextResponse.json({
        formattedLocation: `Location: ${parseFloat(lat).toFixed(4)}, ${parseFloat(lng).toFixed(4)}`,
        placeName: 'Current Location',
        coordinates: {
          lat: parseFloat(lat),
          lng: parseFloat(lng)
        }
      })
    }
  } catch (error) {
    console.error('Error in reverse geocoding API:', error)

    // Final fallback: return coordinates as location
    try {
      const latNum = parseFloat(lat!)
      const lngNum = parseFloat(lng!)

      if (!isNaN(latNum) && !isNaN(lngNum)) {
        console.log('Returning coordinate fallback location')
        return NextResponse.json({
          formattedLocation: `Location: ${latNum.toFixed(4)}, ${lngNum.toFixed(4)}`,
          placeName: 'Current Location',
          coordinates: {
            lat: latNum,
            lng: lngNum
          }
        })
      }
    } catch (fallbackError) {
      console.error('Fallback error:', fallbackError)
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
