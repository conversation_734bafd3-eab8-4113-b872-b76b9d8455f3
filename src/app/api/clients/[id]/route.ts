import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.error('Unauthorized delete attempt:', authError)
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const clientId = params.id
    console.log('🗑️ Deleting client:', clientId, 'by user:', user.email)

    // Check if user has permission to delete (admin or manager)
    if (user.user_metadata?.role !== 'admin' && user.user_metadata?.role !== 'manager') {
      console.error('Insufficient permissions for delete:', user.user_metadata?.role)
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Check if client exists and get its details
    const { data: client, error: fetchError } = await supabase
      .from('clients')
      .select('id, name, custom_id')
      .eq('id', clientId)
      .single()

    if (fetchError) {
      console.error('Error fetching client for deletion:', fetchError)
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      )
    }

    // Check if client has any projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id')
      .eq('client_id', clientId)
      .limit(1)

    if (projectsError) {
      console.error('Error checking client projects:', projectsError)
      return NextResponse.json(
        { error: 'Failed to check client dependencies' },
        { status: 500 }
      )
    }

    if (projects && projects.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete client with existing projects. Please delete all projects first.' },
        { status: 400 }
      )
    }

    console.log('Client to delete:', client)

    // Delete the client
    const { error: deleteError } = await supabase
      .from('clients')
      .delete()
      .eq('id', clientId)

    if (deleteError) {
      console.error('❌ Error deleting client:', deleteError)
      return NextResponse.json(
        { error: deleteError.message },
        { status: 500 }
      )
    }

    console.log('✅ Client deleted successfully:', client.name)

    return NextResponse.json({ 
      success: true,
      message: `Client "${client.name}" deleted successfully`
    })

  } catch (error) {
    console.error('❌ Error in client delete API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete client' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const clientId = params.id

    const { data: client, error } = await supabase
      .from('clients')
      .select(`
        id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link, created_at, updated_at,
        contact_persons (*),
        projects (id, custom_id, name, status, total_amount, created_at)
      `)
      .eq('id', clientId)
      .single()

    if (error) {
      console.error('Error fetching client:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 })
    }

    return NextResponse.json(client)

  } catch (error) {
    console.error('Error in client GET API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch client' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const clientId = params.id
    const updates = await request.json()

    console.log('🔄 Updating client:', clientId, 'with:', Object.keys(updates))

    const { data: client, error } = await supabase
      .from('clients')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', clientId)
      .select('id, custom_id, name, email, phone, address, gst_number, has_gst, client_type, notes, sharepoint_folder_id, sharepoint_folder_url, sharepoint_share_link, created_at, updated_at')
      .single()

    if (error) {
      console.error('❌ Error updating client:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    console.log('✅ Client updated successfully:', client.name)

    return NextResponse.json(client)

  } catch (error) {
    console.error('❌ Error in client update API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update client' },
      { status: 500 }
    )
  }
}
