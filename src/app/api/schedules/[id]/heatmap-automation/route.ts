import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'
import { queueBackgroundJob } from '@/lib/background-jobs'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.error('Unauthorized heatmap automation request:', authError)
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const scheduleId = params.id
    console.log('🗺️ Manual heatmap automation request for schedule:', scheduleId, 'by user:', user.email)

    // Check if user has permission (admin, manager, or pilot)
    const userRole = user.user_metadata?.role
    if (!['admin', 'manager', 'pilot'].includes(userRole)) {
      console.error('Insufficient permissions for heatmap automation:', userRole)
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Verify schedule exists and has SharePoint folder
    const { data: schedule, error: scheduleError } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        sharepoint_folder_id,
        projects!inner (
          id,
          custom_id,
          name,
          clients!inner (
            id,
            custom_id,
            name
          )
        )
      `)
      .eq('id', scheduleId)
      .single()

    if (scheduleError || !schedule) {
      console.error('Schedule not found:', scheduleError)
      return NextResponse.json(
        { error: 'Schedule not found' },
        { status: 404 }
      )
    }

    if (!schedule.sharepoint_folder_id) {
      console.error('Schedule SharePoint folder not created yet:', scheduleId)
      return NextResponse.json(
        { error: 'Schedule SharePoint folder not created yet. Please ensure the schedule folder is created first.' },
        { status: 400 }
      )
    }

    // Queue heatmap automation job
    const jobId = queueBackgroundJob('heatmap_automation', 'schedule', scheduleId, { scheduleId })

    console.log('✅ Heatmap automation queued successfully:', jobId)

    return NextResponse.json({
      success: true,
      message: 'Heatmap automation queued successfully',
      jobId,
      schedule: {
        id: schedule.id,
        custom_id: schedule.custom_id,
        project: schedule.projects[0]?.name,
        client: schedule.projects[0]?.clients[0]?.name
      }
    })

  } catch (error) {
    console.error('❌ Error in heatmap automation API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to queue heatmap automation' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const scheduleId = params.id

    // Get schedule with geo metadata information
    const { data: schedule, error } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        sharepoint_folder_id,
        geo_metadata_link,
        projects!inner (
          id,
          custom_id,
          name,
          clients!inner (
            id,
            custom_id,
            name
          )
        )
      `)
      .eq('id', scheduleId)
      .single()

    if (error || !schedule) {
      console.error('Error fetching schedule:', error)
      return NextResponse.json({ error: 'Schedule not found' }, { status: 404 })
    }

    return NextResponse.json({
      schedule: {
        id: schedule.id,
        custom_id: schedule.custom_id,
        project: schedule.projects[0]?.name,
        client: schedule.projects[0]?.clients[0]?.name,
        sharepoint_folder_id: schedule.sharepoint_folder_id,
        geo_metadata_link: schedule.geo_metadata_link,
        has_sharepoint_folder: !!schedule.sharepoint_folder_id,
        has_geo_metadata: !!schedule.geo_metadata_link
      }
    })

  } catch (error) {
    console.error('Error in heatmap automation status API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch heatmap automation status' },
      { status: 500 }
    )
  }
}