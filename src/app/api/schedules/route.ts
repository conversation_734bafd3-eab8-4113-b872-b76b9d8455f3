import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { createServerSupabaseClient } from '@/lib/auth-server'
import { projectsApi } from '@/lib/api'
import { getDefaultTasksForClientType, createTasksFromTemplates } from '@/lib/default-tasks'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Server-side task creation function using service role client
async function createDefaultTasksServerSide(
  supabaseClient: any,
  projectId: string,
  clientType: string,
  shootDate?: string,
  shootId?: string
): Promise<void> {
  console.log('=== createDefaultTasksServerSide START ===')
  console.log('Parameters:', { projectId, clientType, shootDate, shootId })
  console.log('Task type:', shootId ? 'SHOOT-BASED TASKS' : 'PROJECT-LEVEL TASKS')

  try {
    // Validate inputs
    if (!projectId || !clientType) {
      console.error('❌ VALIDATION ERROR: Missing required parameters:', { projectId, clientType })
      throw new Error('Project ID and client type are required')
    }

    // Check if tasks already exist to prevent duplicates
    const existingTasksQuery = supabaseClient
      .from('tasks')
      .select('id, title, shoot_id')
      .eq('project_id', projectId)

    // If shootId is provided, check for shoot-specific tasks
    if (shootId) {
      existingTasksQuery.eq('shoot_id', shootId)
    } else {
      // Check for project-level tasks (no shoot_id)
      existingTasksQuery.is('shoot_id', null)
    }

    const { data: existingTasks, error: existingTasksError } = await existingTasksQuery

    if (existingTasksError) {
      console.error('Error checking existing tasks:', existingTasksError)
      throw existingTasksError
    }

    if (existingTasks && existingTasks.length > 0) {
      console.log(`Tasks already exist for ${shootId ? 'shoot ' + shootId : 'project ' + projectId}:`)
      existingTasks.forEach(task => {
        console.log(`  - ${task.title} (${task.shoot_id ? 'shoot task' : 'project task'})`)
      })
      console.log('=== createDefaultTasksServerSide END (SKIPPED - DUPLICATES) ===')
      return // Don't create duplicate tasks
    }

    // Get default task templates for this client type
    const templates = getDefaultTasksForClientType(clientType)
    console.log('Templates found for client type "' + clientType + '":', templates.length, 'templates')

    if (templates.length === 0) {
      console.log('No templates found for client type:', clientType)
      return
    }

    // Get users by role for task assignment
    const { data: users, error: usersError } = await supabaseClient
      .from('users')
      .select('id, name, role')
      .order('role')

    if (usersError) {
      console.error('Error fetching users:', usersError)
      throw usersError
    }

    const usersByRole = {
      pilot: users?.filter((u: any) => u.role === 'pilot') || [],
      editor: users?.filter((u: any) => u.role === 'editor') || [],
      admin: users?.filter((u: any) => u.role === 'admin' || u.role === 'manager') || []
    }

    console.log('Users by role:', {
      pilot: usersByRole.pilot.length,
      editor: usersByRole.editor.length,
      admin: usersByRole.admin.length
    })

    // Create a mapping of role to first available user ID
    const roleToUserId: Record<string, string> = {}
    if (usersByRole.pilot.length > 0) roleToUserId.pilot = usersByRole.pilot[0].id
    if (usersByRole.editor.length > 0) roleToUserId.editor = usersByRole.editor[0].id
    if (usersByRole.admin.length > 0) roleToUserId.admin = usersByRole.admin[0].id

    // Fallback: if no specific role users exist, use admin for all tasks
    const fallbackUserId = usersByRole.admin.length > 0 ? usersByRole.admin[0].id : ''
    if (!roleToUserId.pilot && fallbackUserId) roleToUserId.pilot = fallbackUserId
    if (!roleToUserId.editor && fallbackUserId) roleToUserId.editor = fallbackUserId
    if (!roleToUserId.admin && fallbackUserId) roleToUserId.admin = fallbackUserId

    console.log('Role to user mapping:', roleToUserId)

    // Separate shoot-based and project-level tasks
    const shootBasedTemplates = templates.filter(t => !t.isProjectTask)
    const projectLevelTemplates = templates.filter(t => t.isProjectTask)

    let createdCount = 0
    let skippedCount = 0

    // Create shoot-based tasks if shootId is provided
    if (shootId && shootBasedTemplates.length > 0) {
      console.log('Creating shoot-based tasks for shoot:', shootId)
      const shootTaskForms = createTasksFromTemplates(shootBasedTemplates, projectId, roleToUserId, shootDate, shootId)

      for (const taskForm of shootTaskForms) {
        if (taskForm.assigned_to) {
          console.log('Creating shoot task:', {
            title: taskForm.title,
            assigned_to: taskForm.assigned_to,
            project_id: taskForm.project_id,
            shoot_id: taskForm.shoot_id
          })
          try {
            const { data: createdTask, error: createError } = await supabaseClient
              .from('tasks')
              .insert(taskForm)
              .select()
              .single()

            if (createError) {
              console.error('Failed to create shoot task:', createError)
              throw createError
            }

            console.log('Shoot task created successfully:', createdTask.id, createdTask.title)
            createdCount++
          } catch (taskCreateError) {
            console.error('Failed to create shoot task:', taskCreateError)
            throw taskCreateError
          }
        } else {
          console.log('Skipping shoot task without assigned user:', taskForm.title)
          skippedCount++
        }
      }
    }

    console.log(`=== createDefaultTasksServerSide END ===`)
    console.log(`Summary: ${createdCount} tasks created, ${skippedCount} tasks skipped`)
    console.log(`Task type: ${shootId ? 'SHOOT-BASED' : 'PROJECT-LEVEL'}`)
    console.log('=======================================')
  } catch (error) {
    console.error('❌ CRITICAL ERROR in createDefaultTasksServerSide:', error)
    console.error('Parameters that caused the error:', { projectId, clientType, shootDate, shootId })
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    })
    console.log('=== createDefaultTasksServerSide END (ERROR) ===')
    throw error
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabaseAuth = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { schedule, vendors } = await request.json()

    if (!schedule) {
      return NextResponse.json(
        { error: 'Schedule data is required' },
        { status: 400 }
      )
    }

    console.log('🚀 Creating schedule via API endpoint:', {
      project_id: schedule.project_id,
      scheduled_date: schedule.scheduled_date,
      location: schedule.location,
      user_id: user.id
    })

    // Use service role client for server-side operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Normalize vendors payload
    const normalizedVendors = Array.isArray(vendors)
      ? vendors.map((v: any) => ({
          vendor_id: String(v.vendor_id),
          cost: Number(v.cost) || 0,
          notes: v.notes || ''
        }))
      : []

    // Call the PostgreSQL function to create schedule with vendors
    const { data, error } = await supabase.rpc('create_schedule_with_vendors', {
      p_project_id: schedule.project_id,
      p_scheduled_date: schedule.scheduled_date,
      p_scheduled_end_date: schedule.scheduled_end_date,
      p_pilot_id: schedule.pilot_id,
      p_amount: schedule.amount,
      p_location: schedule.location,
      p_google_maps_link: schedule.google_maps_link,
      p_notes: schedule.notes,
      p_is_recurring: schedule.is_recurring,
      p_recurring_pattern: schedule.recurring_pattern,
      p_is_outsourced: !!schedule.is_outsourced,
      p_vendors: normalizedVendors
    })

    if (error) {
      console.error('❌ Error creating schedule:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    console.log(`✅ Schedule created: ${data.custom_id} (ID: ${data.id})`)

    // Recalculate project total
    try {
      await projectsApi.recalculateProjectTotal(schedule.project_id)
    } catch (projectError) {
      console.error('⚠️ Failed to recalculate project total:', projectError)
      // Don't fail the schedule creation if project total calculation fails
    }

    // Create default tasks with due dates based on schedule date
    try {
      const { data: project } = await supabase
        .from('projects')
        .select(`
          id,
          client_id,
          clients!inner (
            client_type
          )
        `)
        .eq('id', schedule.project_id)
        .single()

      if (project?.clients?.client_type) {
        console.log('Creating default tasks for new schedule:', {
          projectId: schedule.project_id,
          clientType: project.clients.client_type,
          scheduleDate: schedule.scheduled_date,
          scheduleId: data.id
        })

        // Create tasks directly using service role client instead of projectsApi
        await createDefaultTasksServerSide(
          supabase,
          schedule.project_id,
          project.clients.client_type,
          schedule.scheduled_date,
          data.id // Pass the schedule ID for shoot-based tasks
        )

        console.log('Default tasks created successfully for schedule:', data.id)
      } else {
        console.log('No client type found, skipping task creation for schedule:', data.id)
      }
    } catch (taskError) {
      console.error('⚠️ Failed to create default tasks for schedule:', taskError)
      console.error('Task creation error details:', {
        message: taskError instanceof Error ? taskError.message : 'Unknown error',
        stack: taskError instanceof Error ? taskError.stack : undefined
      })
      // Don't fail the schedule creation if task creation fails
    }

    // Queue SharePoint folder creation as background job for fast response
    try {
      console.log('Queuing SharePoint folder creation for schedule:', data.id)

      // Ensure background job system is initialized
      const { initializeBackgroundJobs } = await import('@/lib/init-background-jobs')
      initializeBackgroundJobs()

      const { queueSharePointFolderCreation } = await import('@/lib/background-jobs')

      // Always use background processing for fast form submission
      const jobId = await queueSharePointFolderCreation('schedule', data.id, {
        scheduleId: data.id,
        customId: data.custom_id,
        scheduledDate: schedule.scheduled_date
      }, {
        executeSync: false,        // Never execute synchronously for fast response
        fallbackToSync: false      // Don't fallback to sync to keep it fast
      })

      console.log('SharePoint folder creation queued for schedule:', {
        scheduleId: data.id,
        customId: data.custom_id,
        jobId,
        executionType: 'background'
      })
    } catch (jobError) {
      console.error('⚠️ Failed to queue SharePoint folder creation for schedule:', jobError)
      // Don't fail the schedule creation if SharePoint folder creation fails
      // This ensures the user can still create schedules even if SharePoint is down
    }

    // Return the created schedule
    return NextResponse.json(data)

  } catch (error) {
    console.error('❌ Error in schedule creation API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create schedule' },
      { status: 500 }
    )
  }
}
