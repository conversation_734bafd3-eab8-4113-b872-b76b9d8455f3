import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Heatmap Automation System')
    
    // Check authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Test 1: Check if required environment variables are set
    const requiredEnvVars = [
      'MICROSOFT_CLIENT_ID',
      'MICROSOFT_CLIENT_SECRET', 
      'MICROSOFT_TENANT_ID'
    ]

    const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName])
    
    if (missingEnvVars.length > 0) {
      return NextResponse.json({
        success: false,
        error: `Missing environment variables: ${missingEnvVars.join(', ')}`,
        tests: {
          envVars: false,
          functions: false,
          database: false
        }
      })
    }

    // Test 2: Check if functions can be imported
    let functionsTest = false
    try {
      const { processHeatmapAutomation } = await import('@/lib/heatmap-automation')
      const { queueBackgroundJob } = await import('@/lib/background-jobs')
      functionsTest = typeof processHeatmapAutomation === 'function' && typeof queueBackgroundJob === 'function'
    } catch (error) {
      console.error('Function import test failed:', error)
    }

    // Test 3: Check database schema
    let databaseTest = false
    try {
      const { data: schedules, error } = await supabase
        .from('schedules')
        .select('id, custom_id, sharepoint_folder_id, geo_metadata_link')
        .limit(1)
      
      databaseTest = !error
    } catch (error) {
      console.error('Database test failed:', error)
    }

    // Test 4: Get schedules with SharePoint folders for testing
    const { data: testSchedules, error: schedulesError } = await supabase
      .from('schedules')
      .select(`
        id,
        custom_id,
        sharepoint_folder_id,
        geo_metadata_link,
        projects!inner (
          id,
          custom_id,
          name,
          clients!inner (
            id,
            custom_id,
            name
          )
        )
      `)
      .not('sharepoint_folder_id', 'is', null)
      .limit(5)

    return NextResponse.json({
      success: true,
      message: 'Heatmap automation system test completed',
      tests: {
        envVars: true,
        functions: functionsTest,
        database: databaseTest
      },
      system: {
        user: user.email,
        timestamp: new Date().toISOString(),
        availableSchedules: testSchedules?.length || 0,
        sampleSchedules: testSchedules?.map(s => ({
          id: s.id,
          custom_id: s.custom_id,
          project: s.projects[0]?.name,
          client: s.projects[0]?.clients[0]?.name,
          has_sharepoint_folder: !!s.sharepoint_folder_id,
          has_geo_metadata: !!s.geo_metadata_link
        })) || []
      },
      nextSteps: [
        'Upload drone files (photos, videos, SRT) to a schedule SharePoint folder',
        'Complete the "File Upload" task for that schedule',
        'Check the generated geo_metadata.json file',
        'View the heatmap data in the Map page'
      ]
    })

  } catch (error) {
    console.error('❌ Heatmap automation test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Test failed',
      tests: {
        envVars: false,
        functions: false,
        database: false
      }
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { scheduleId } = await request.json()
    
    if (!scheduleId) {
      return NextResponse.json({ error: 'Schedule ID is required' }, { status: 400 })
    }

    // Check authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('🧪 Testing heatmap automation for schedule:', scheduleId)

    // Import and run heatmap automation
    const { processHeatmapAutomation } = await import('@/lib/heatmap-automation')
    const result = await processHeatmapAutomation(scheduleId)

    return NextResponse.json({
      success: true,
      message: 'Heatmap automation test completed',
      result,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Heatmap automation test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Test failed'
    }, { status: 500 })
  }
}