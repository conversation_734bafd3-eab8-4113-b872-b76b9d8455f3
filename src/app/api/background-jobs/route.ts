import { NextRequest, NextResponse } from 'next/server'
import { getJobStatus, getAllJobs } from '@/lib/background-jobs'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const jobId = searchParams.get('jobId')

    if (jobId) {
      // Get specific job status
      const job = getJobStatus(jobId)
      
      if (!job) {
        return NextResponse.json(
          { error: 'Job not found' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        job: {
          id: job.id,
          type: job.type,
          entityType: job.entityType,
          entityId: job.entityId,
          status: job.status,
          attempts: job.attempts,
          maxAttempts: job.maxAttempts,
          createdAt: job.createdAt,
          updatedAt: job.updatedAt,
          error: job.error
        }
      })
    } else {
      // Get all jobs (for debugging)
      const jobs = getAllJobs()
      
      return NextResponse.json({
        success: true,
        jobs: jobs.map(job => ({
          id: job.id,
          type: job.type,
          entityType: job.entityType,
          entityId: job.entityId,
          status: job.status,
          attempts: job.attempts,
          maxAttempts: job.maxAttempts,
          createdAt: job.createdAt,
          updatedAt: job.updatedAt,
          error: job.error
        }))
      })
    }
  } catch (error) {
    console.error('Error in background jobs API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to get job status' },
      { status: 500 }
    )
  }
}