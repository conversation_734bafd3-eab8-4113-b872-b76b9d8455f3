import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'
import type { CreateNotificationRequest, NotificationFilters } from '@/types/notifications'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const filters: NotificationFilters = {
      read: searchParams.get('read') === 'true' ? true : searchParams.get('read') === 'false' ? false : undefined,
      type: searchParams.get('type') as any,
      category: searchParams.get('category') as any,
      priority: searchParams.get('priority') as any,
      limit: parseInt(searchParams.get('limit') || '50'),
      offset: parseInt(searchParams.get('offset') || '0')
    }

    let query = supabase
      .from('notifications')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (filters.read !== undefined) {
      query = query.eq('read', filters.read)
    }
    if (filters.type) {
      query = query.eq('type', filters.type)
    }
    if (filters.category) {
      query = query.eq('category', filters.category)
    }
    if (filters.priority) {
      query = query.eq('priority', filters.priority)
    }

    query = query.range(filters.offset!, filters.offset! + filters.limit! - 1)

    const { data: notifications, error } = await query

    if (error) {
      console.error('Error fetching notifications:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(notifications)

  } catch (error) {
    console.error('Error in notifications GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const notificationData: CreateNotificationRequest = await request.json()

    // Validate required fields
    if (!notificationData.title || !notificationData.message || !notificationData.type || !notificationData.category) {
      return NextResponse.json(
        { error: 'Missing required fields: title, message, type, category' },
        { status: 400 }
      )
    }

    const { data: notification, error } = await supabase
      .from('notifications')
      .insert({
        ...notificationData,
        priority: notificationData.priority || 'medium',
        read: false,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating notification:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // TODO: Send push notification if user has enabled it
    // TODO: Send email notification if user has enabled it

    return NextResponse.json(notification, { status: 201 })

  } catch (error) {
    console.error('Error in notifications POST:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
