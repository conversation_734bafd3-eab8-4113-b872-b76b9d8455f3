import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'
import type { NotificationStats } from '@/types/notifications'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get user from session
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get notification stats with better error handling
    const [unreadResult, totalResult, priorityResult] = await Promise.all([
      supabase
        .from('notifications')
        .select('id', { count: 'exact' })
        .eq('user_id', user.id)
        .eq('read', false),
      
      supabase
        .from('notifications')
        .select('id', { count: 'exact' })
        .eq('user_id', user.id),
      
      supabase
        .from('notifications')
        .select('priority')
        .eq('user_id', user.id)
        .eq('read', false)
        .eq('priority', 'high')
    ])

    // Check for errors in individual queries
    if (unreadResult.error) {
      console.error('Error fetching unread notifications:', unreadResult.error)
      throw unreadResult.error
    }
    
    if (totalResult.error) {
      console.error('Error fetching total notifications:', totalResult.error)
      throw totalResult.error
    }
    
    if (priorityResult.error) {
      console.error('Error fetching priority notifications:', priorityResult.error)
      throw priorityResult.error
    }

    const stats = {
      unread: unreadResult.count || 0,
      total: totalResult.count || 0,
      high_priority: priorityResult.data?.length || 0
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching notification stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
