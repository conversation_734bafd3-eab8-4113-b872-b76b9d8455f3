import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { action, notification_ids, filters } = await request.json()

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      )
    }

    let query = supabase
      .from('notifications')
      .update({
        updated_at: new Date().toISOString()
      })
      .eq('user_id', user.id)

    // Apply action-specific updates
    switch (action) {
      case 'mark_read':
        query = query.update({
          read: true,
          read_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        break
      case 'mark_unread':
        query = query.update({
          read: false,
          read_at: null,
          updated_at: new Date().toISOString()
        })
        break
      case 'delete':
        query = supabase
          .from('notifications')
          .delete()
          .eq('user_id', user.id)
        break
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    // Apply filters
    if (notification_ids && notification_ids.length > 0) {
      query = query.in('id', notification_ids)
    } else if (filters) {
      if (filters.read !== undefined) {
        query = query.eq('read', filters.read)
      }
      if (filters.type) {
        query = query.eq('type', filters.type)
      }
      if (filters.category) {
        query = query.eq('category', filters.category)
      }
      if (filters.priority) {
        query = query.eq('priority', filters.priority)
      }
    }

    const { data, error } = await query.select()

    if (error) {
      console.error('Error performing bulk action:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      affected_count: data?.length || 0,
      action
    })

  } catch (error) {
    console.error('Error in notifications bulk action:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
