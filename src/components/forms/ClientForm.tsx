'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { useCreateClient, useUpdateClient } from '@/hooks/useApi'
import toast from 'react-hot-toast'
import type { Client } from '@/types'
import { CLIENT_TYPES } from '@/lib/constants'

const clientSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email').optional().or(z.literal('')),
  phone: z.string().optional(),
  address: z.string().optional(),
  gst_number: z.string().optional(),
  has_gst: z.boolean(),
  client_type: z.string().optional(),
  notes: z.string().optional(),
})

type ClientFormData = z.infer<typeof clientSchema>

interface ClientFormProps {
  client?: Client
  onSuccess?: (client: Client) => void
  onCancel?: () => void
  initialName?: string
}

export function ClientForm({ client, onSuccess, onCancel, initialName }: ClientFormProps) {
  const isEditing = !!client
  const { createClient, loading: createLoading } = useCreateClient()
  const { updateClient, loading: updateLoading } = useUpdateClient()
  const loading = createLoading || updateLoading
  const [previewId, setPreviewId] = useState<string | null>(null)

  // Load preview ID only in browser and only for create mode
  // Avoids blocking submit and avoids refetching after submit
  if (typeof window !== 'undefined' && !isEditing) {
    // useEffect needs to be declared in component scope
  }

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ClientFormData>({
    resolver: zodResolver(clientSchema),
    defaultValues: client ? {
      name: client.name,
      email: client.email || '',
      phone: client.phone || '',
      address: client.address || '',
      gst_number: client.gst_number || '',
      has_gst: client.has_gst,
      client_type: client.client_type || '',
      notes: client.notes || '',
    } : {
      name: initialName || '',
      email: '',
      phone: '',
      address: '',
      gst_number: '',
      has_gst: false,
      client_type: '',
      notes: '',
    },
  })

  // Effect: fetch preview custom_id on mount for create mode
  // Placed after form setup so it can run once on component render
  // Ensures no changes to actual create/insert logic
  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    let mounted = true
    async function loadPreview() {
      try {
        const { peekNextId } = await import('@/lib/api')
        const id = await peekNextId('client')
        if (mounted) setPreviewId(id)
      } catch (e) {
        console.warn('ClientForm preview load failed:', e)
      }
    }
    if (typeof window !== 'undefined' && !isEditing) {
      loadPreview()
    }
    return () => {
      mounted = false
    }
  }, [isEditing])

  const onSubmit = async (data: ClientFormData) => {
    try {
      // Convert empty strings to null for optional fields
      const cleanData = {
        ...data,
        email: data.email || null,
        phone: data.phone || null,
        address: data.address || null,
        gst_number: data.gst_number || null,
        has_gst: data.has_gst,
        client_type: data.client_type || null,
        notes: data.notes || null,
      }

      let result: Client
      if (isEditing) {
        result = await updateClient(client.id, cleanData)
        toast.success('Client updated successfully')
      } else {
        result = await createClient(cleanData)
        toast.success('Client created successfully')
        reset()
      }

      onSuccess?.(result)
    } catch (error: any) {
      toast.error(error.message || 'Failed to save client')
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {isEditing && client?.custom_id && (
        <div className="text-xs text-muted-foreground -mb-2">
          ID: {client.custom_id}
        </div>
      )}
      {!isEditing && previewId && (
        <div className="text-xs text-muted-foreground -mb-2">
          ID (preview): {previewId}
        </div>
      )}
      <div>
        <Label htmlFor="name">Name *</Label>
        <Input
          id="name"
          {...register('name')}
          placeholder="Enter client name"
          className="mt-1"
        />
        {errors.name && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.name.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          {...register('email')}
          placeholder="Enter email address"
          className="mt-1"
        />
        {errors.email && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.email.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="phone">Phone</Label>
        <Input
          id="phone"
          {...register('phone')}
          placeholder="Enter phone number"
          className="mt-1"
        />
        {errors.phone && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.phone.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="address">Address</Label>
        <Input
          id="address"
          {...register('address')}
          placeholder="Enter address"
          className="mt-1"
        />
        {errors.address && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.address.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="client_type">Client Type</Label>
        <select
          id="client_type"
          {...register('client_type')}
          className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <option value="">Select client type</option>
          {CLIENT_TYPES.map((type) => (
            <option key={type} value={type}>
              {type}
            </option>
          ))}
        </select>
        {errors.client_type && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.client_type.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="gst_number">GST Number</Label>
        <Input
          id="gst_number"
          {...register('gst_number')}
          placeholder="Enter GST number (e.g., 22AAAAA0000A1Z5)"
          className="mt-1"
        />
        {errors.gst_number && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.gst_number.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="has_gst">GST Registration</Label>
        <div className="mt-1 flex items-center space-x-2">
          <input
            id="has_gst"
            type="checkbox"
            {...register('has_gst')}
            className="rounded border-border text-primary focus:ring-primary"
          />
          <label htmlFor="has_gst" className="text-sm text-foreground">
            Client has GST registration
          </label>
        </div>
        {errors.has_gst && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.has_gst.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="notes">Notes</Label>
        <textarea
          id="notes"
          {...register('notes')}
          placeholder="Enter any additional notes"
          className="mt-1 flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          rows={3}
        />
        {errors.notes && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.notes.message}</p>
        )}
      </div>

      <div className="flex gap-3 pt-4">
        <Button
          type="submit"
          disabled={loading}
          className="flex-1"
        >
          {loading ? 'Saving...' : isEditing ? 'Update Client' : 'Create Client'}
        </Button>
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
        )}
      </div>
    </form>
  )
}
