'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { createDebouncedPlaceSearch, getPlaceDetails, type PlaceSearchResult } from '@/lib/maps-utils'
import { MapPin, Search, Loader2, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface LocationSearchProps {
  value: string
  onChange: (location: string) => void
  onLocationSelect?: (locationInfo: { formattedLocation: string; coordinates?: { lat: number; lng: number } }) => void
  placeholder?: string
  label?: string
  disabled?: boolean
  error?: string
  className?: string
  showCurrentLocationToggle?: boolean
}

export function LocationSearch({
  value,
  onChange,
  onLocationSelect,
  placeholder = "Search for a location...",
  label,
  disabled = false,
  error,
  className,
  showCurrentLocationToggle = true
}: LocationSearchProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchResults, setSearchResults] = useState<PlaceSearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const [searchError, setSearchError] = useState<string | null>(null)
  const [useCurrentLocation, setUseCurrentLocation] = useState(false)
  const [gettingLocation, setGettingLocation] = useState(false)
  
  const dropdownRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const debouncedSearch = useRef(createDebouncedPlaceSearch(300))

  // Handle search
  const handleSearch = useCallback((query: string) => {
    if (!query || query.trim().length < 2 || useCurrentLocation) {
      setSearchResults([])
      setIsOpen(false)
      setSearchError(null)
      return
    }

    setIsSearching(true)
    setSearchError(null)
    debouncedSearch.current(query, (results, error) => {
      setSearchResults(results)
      setIsSearching(false)
      setIsOpen(true) // Always open to show results or error
      setSelectedIndex(-1)

      if (error) {
        setSearchError(error)
      } else if (results.length === 0) {
        setSearchError('No locations found. Try a different search term.')
      }
    })
  }, [useCurrentLocation])

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    onChange(newValue)
    handleSearch(newValue)
  }

  // Handle place selection
  const handlePlaceSelect = async (place: PlaceSearchResult) => {
    setIsSearching(true)
    try {
      const locationInfo = await getPlaceDetails(place.place_id)
      if (locationInfo) {
        onChange(locationInfo.formattedLocation)
        onLocationSelect?.({
          formattedLocation: locationInfo.formattedLocation,
          coordinates: locationInfo.coordinates
        })
      } else {
        onChange(place.description)
        onLocationSelect?.({
          formattedLocation: place.description
        })
      }
    } catch (error) {
      console.error('Error getting place details:', error)
      onChange(place.description)
      onLocationSelect?.({
        formattedLocation: place.description
      })
    } finally {
      setIsSearching(false)
      setIsOpen(false)
      setSearchResults([])
    }
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || searchResults.length === 0) {
      if (e.key === 'ArrowDown' && value.trim().length >= 2) {
        handleSearch(value)
      }
      return
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < searchResults.length - 1 ? prev + 1 : 0
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : searchResults.length - 1
        )
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0 && selectedIndex < searchResults.length) {
          handlePlaceSelect(searchResults[selectedIndex])
        }
        break
      case 'Escape':
        setIsOpen(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Get current location
  const getCurrentLocation = useCallback(async () => {
    console.log('getCurrentLocation called')

    if (!navigator.geolocation) {
      console.error('Geolocation not supported')
      setSearchError('Geolocation is not supported by this browser')
      return
    }

    // Check if we're on HTTPS or localhost
    const isSecure = window.location.protocol === 'https:' || window.location.hostname === 'localhost'
    if (!isSecure) {
      console.error('Geolocation requires HTTPS')
      setSearchError('Location access requires a secure connection (HTTPS)')
      return
    }

    setGettingLocation(true)
    setSearchError(null)

    try {
      console.log('Requesting geolocation...')
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          (pos) => {
            console.log('Geolocation success:', pos.coords)
            resolve(pos)
          },
          (err) => {
            console.error('Geolocation error:', err)
            reject(err)
          },
          {
            enableHighAccuracy: true,
            timeout: 15000, // Increased timeout
            maximumAge: 300000 // 5 minutes
          }
        )
      })

      const { latitude, longitude } = position.coords
      console.log('Got coordinates:', { latitude, longitude })

      // Reverse geocode to get address
      console.log('Calling reverse geocode API...')
      const response = await fetch(`/api/places/reverse-geocode?lat=${latitude}&lng=${longitude}`)

      console.log('Reverse geocode response status:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Reverse geocode API error:', response.status, errorText)
        throw new Error(`Failed to get address for current location: ${response.status}`)
      }

      const data = await response.json()
      console.log('Reverse geocode data:', data)

      if (data.formattedLocation) {
        console.log('Setting location:', data.formattedLocation)
        onChange(data.formattedLocation)
        onLocationSelect?.({
          formattedLocation: data.formattedLocation,
          coordinates: { lat: latitude, lng: longitude }
        })
      } else {
        throw new Error('Could not determine address for current location')
      }
    } catch (error: any) {
      console.error('Error getting current location:', error)

      // Handle geolocation errors
      if (error.code === 1) {
        setSearchError('Location access denied. Please enable location permissions in your browser.')
      } else if (error.code === 2) {
        setSearchError('Location unavailable. Please check your GPS/network connection.')
      } else if (error.code === 3) {
        setSearchError('Location request timed out. Please try again.')
      } else if (error.message?.includes('Failed to get address')) {
        setSearchError('Got your location but could not determine the address. Please try searching manually.')
      } else {
        setSearchError(`Failed to get current location: ${error.message || 'Unknown error'}`)
      }
      setUseCurrentLocation(false)
    } finally {
      setGettingLocation(false)
    }
  }, [onChange, onLocationSelect])

  // Handle current location toggle
  const handleCurrentLocationToggle = useCallback((checked: boolean) => {
    setUseCurrentLocation(checked)
    if (checked) {
      getCurrentLocation()
    } else {
      onChange('')
      setSearchError(null)
    }
  }, [getCurrentLocation, onChange])

  // Clear search
  const handleClear = () => {
    onChange('')
    setSearchResults([])
    setIsOpen(false)
    setUseCurrentLocation(false)
    setSearchError(null)
    inputRef.current?.focus()
  }

  return (
    <div className={cn("relative z-10", className)} ref={dropdownRef}>
      {(label || showCurrentLocationToggle) && (
        <div className="flex items-center justify-between mb-2">
          {label && (
            <Label htmlFor="location-search" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {label}
            </Label>
          )}
          {showCurrentLocationToggle && (
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="useCurrentLocation"
                checked={useCurrentLocation}
                onChange={(e) => handleCurrentLocationToggle(e.target.checked)}
                disabled={disabled || gettingLocation}
                className="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
              />
              <Label htmlFor="useCurrentLocation" className="text-sm text-gray-600 dark:text-gray-400 flex items-center gap-1 cursor-pointer">
                {gettingLocation ? (
                  <>
                    <Loader2 className="w-3 h-3 animate-spin" />
                    Getting location...
                  </>
                ) : (
                  <>
                    <MapPin className="w-3 h-3" />
                    Use current location
                  </>
                )}
              </Label>
            </div>
          )}
        </div>
      )}
      
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          {isSearching ? (
            <Loader2 className="w-4 h-4 text-gray-400 animate-spin" />
          ) : (
            <Search className="w-4 h-4 text-gray-400" />
          )}
        </div>
        
        <Input
          ref={inputRef}
          id="location-search"
          type="text"
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={useCurrentLocation ? "Using current location..." : placeholder}
          disabled={disabled || useCurrentLocation || gettingLocation}
          className={cn(
            "pl-10 pr-10",
            error && "border-red-500 focus:border-red-500 focus:ring-red-500",
            (useCurrentLocation || gettingLocation) && "bg-gray-100 dark:bg-gray-800"
          )}
        />
        
        {value && !disabled && !gettingLocation && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="absolute inset-y-0 right-0 px-3 hover:bg-transparent"
          >
            <X className="w-4 h-4 text-gray-400 hover:text-gray-600" />
          </Button>
        )}
      </div>

      {error && (
        <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}

      {/* Search Results Dropdown */}
      {isOpen && searchResults.length > 0 && !useCurrentLocation && !gettingLocation && (
        <div className="absolute z-[9999] w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl max-h-60 overflow-y-auto" style={{ boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' }}>
          {searchResults.map((place, index) => (
            <button
              key={place.place_id}
              type="button"
              onClick={() => handlePlaceSelect(place)}
              className={cn(
                "w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 focus:bg-gray-50 dark:focus:bg-gray-700 focus:outline-none border-b border-gray-100 dark:border-gray-700 last:border-b-0",
                selectedIndex === index && "bg-gray-50 dark:bg-gray-700"
              )}
            >
              <div className="flex items-start gap-3">
                <MapPin className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {place.structured_formatting.main_text}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {place.structured_formatting.secondary_text}
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}

      {/* Error or No results message */}
      {isOpen && !isSearching && searchResults.length === 0 && value.trim().length >= 2 && !useCurrentLocation && !gettingLocation && (
        <div className={`absolute z-[9999] w-full mt-1 border rounded-lg shadow-xl p-4 ${
          searchError
            ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
            : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
        }`}>
          <div className={`text-sm text-center ${
            searchError
              ? 'text-red-800 dark:text-red-200'
              : 'text-gray-500 dark:text-gray-400'
          }`}>
            {searchError || `No locations found for "${value}"`}
          </div>
          <div className="text-xs text-center mt-1 text-gray-400 dark:text-gray-500">
            {searchError
              ? 'You can still enter the location manually'
              : 'Try a different search term or enter the location manually'
            }
          </div>
        </div>
      )}
    </div>
  )
}
