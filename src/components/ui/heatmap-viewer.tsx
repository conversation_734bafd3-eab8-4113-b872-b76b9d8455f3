'use client'

import { useState, useEffect, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  MapPin, 
  Camera, 
  Video, 
  Download, 
  RefreshCw, 
  AlertCircle,
  CheckCircle,
  Clock,
  FileImage,
  FileVideo
} from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import type { Schedule } from '@/types'

interface GeoMetadata {
  type: 'photo' | 'video'
  filename: string
  lat?: number
  lng?: number
  timestamp?: string
  start?: { lat: number; lng: number }
  end?: { lat: number; lng: number }
}

interface ScheduleWithMetadata {
  id: string
  custom_id: string
  project: string
  client: string
  sharepoint_folder_id: string | null
  geo_metadata_link: string | null
  has_sharepoint_folder: boolean
  has_geo_metadata: boolean
}

interface HeatmapViewerProps {
  className?: string
}

export function HeatmapViewer({ className }: HeatmapViewerProps) {
  const { data: schedules, loading: schedulesLoading, refetch: refetchSchedules } = useApi<ScheduleWithMetadata[]>('/api/schedules')
  const [selectedScheduleId, setSelectedScheduleId] = useState<string>('')
  const [selectedSchedule, setSelectedSchedule] = useState<ScheduleWithMetadata | null>(null)
  const [geoMetadata, setGeoMetadata] = useState<GeoMetadata[]>([])
  const [loadingMetadata, setLoadingMetadata] = useState(false)
  const [processingAutomation, setProcessingAutomation] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Filter schedules that have SharePoint folders
  const availableSchedules = useMemo(() => {
    return schedules?.filter(s => s.has_sharepoint_folder) || []
  }, [schedules])

  useEffect(() => {
    if (selectedScheduleId) {
      const schedule = availableSchedules.find(s => s.id === selectedScheduleId)
      setSelectedSchedule(schedule || null)
      
      if (schedule?.has_geo_metadata) {
        loadGeoMetadata(schedule.geo_metadata_link!)
      } else {
        setGeoMetadata([])
      }
    } else {
      setSelectedSchedule(null)
      setGeoMetadata([])
    }
  }, [selectedScheduleId])

  const loadGeoMetadata = async (metadataLink: string) => {
    setLoadingMetadata(true)
    setError(null)
    
    try {
      // Convert SharePoint sharing link to direct download link
      const response = await fetch(metadataLink)
      if (!response.ok) {
        throw new Error('Failed to fetch geo metadata')
      }
      
      const metadata = await response.json()
      setGeoMetadata(Array.isArray(metadata) ? metadata : [])
    } catch (err) {
      console.error('Error loading geo metadata:', err)
      setError(err instanceof Error ? err.message : 'Failed to load geo metadata')
      setGeoMetadata([])
    } finally {
      setLoadingMetadata(false)
    }
  }

  const triggerHeatmapAutomation = async () => {
    if (!selectedScheduleId) return

    setProcessingAutomation(true)
    setError(null)

    try {
      const response = await fetch(`/api/schedules/${selectedScheduleId}/heatmap-automation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to trigger heatmap automation')
      }

      const result = await response.json()
      console.log('Heatmap automation triggered:', result)

      // Refresh schedule data after a short delay
      setTimeout(() => {
        refetchSchedules()
      }, 2000)

    } catch (err) {
      console.error('Error triggering heatmap automation:', err)
      setError(err instanceof Error ? err.message : 'Failed to trigger heatmap automation')
    } finally {
      setProcessingAutomation(false)
    }
  }

  const downloadGeoMetadata = () => {
    if (!selectedSchedule?.geo_metadata_link) return

    const link = document.createElement('a')
    link.href = selectedSchedule.geo_metadata_link
    link.download = `${selectedSchedule.custom_id}_geo_metadata.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const getLocationSummary = () => {
    if (geoMetadata.length === 0) return null

    const photos = geoMetadata.filter(item => item.type === 'photo')
    const videos = geoMetadata.filter(item => item.type === 'video')
    
    const allCoordinates: Array<{ lat: number; lng: number }> = []
    
    photos.forEach(photo => {
      if (photo.lat && photo.lng) {
        allCoordinates.push({ lat: photo.lat, lng: photo.lng })
      }
    })
    
    videos.forEach(video => {
      if (video.start) allCoordinates.push(video.start)
      if (video.end) allCoordinates.push(video.end)
    })

    if (allCoordinates.length === 0) return null

    const bounds = {
      north: Math.max(...allCoordinates.map(c => c.lat)),
      south: Math.min(...allCoordinates.map(c => c.lat)),
      east: Math.max(...allCoordinates.map(c => c.lng)),
      west: Math.min(...allCoordinates.map(c => c.lng))
    }

    const center = {
      lat: (bounds.north + bounds.south) / 2,
      lng: (bounds.east + bounds.west) / 2
    }

    return {
      totalFiles: geoMetadata.length,
      photos: photos.length,
      videos: videos.length,
      bounds,
      center,
      coordinates: allCoordinates
    }
  }

  const locationSummary = getLocationSummary()

  if (schedulesLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Loading schedules...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Schedule Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Heatmap Data Viewer
          </CardTitle>
          <CardDescription>
            Select a schedule to view its geolocation metadata from uploaded drone files
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label className="text-sm font-medium mb-2 block">Select Schedule</label>
              <Select value={selectedScheduleId} onValueChange={setSelectedScheduleId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a schedule with SharePoint folder..." />
                </SelectTrigger>
                <SelectContent>
                  {availableSchedules.map(schedule => (
                    <SelectItem key={schedule.id} value={schedule.id}>
                      <div className="flex items-center gap-2">
                        <span>{schedule.custom_id}</span>
                        <span className="text-muted-foreground">-</span>
                        <span>{schedule.project}</span>
                        {schedule.has_geo_metadata && (
                          <Badge variant="secondary" className="ml-2">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Has Data
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {selectedSchedule && (
              <div className="flex gap-2">
                <Button
                  onClick={triggerHeatmapAutomation}
                  disabled={processingAutomation}
                  variant="outline"
                >
                  {processingAutomation ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      {selectedSchedule.has_geo_metadata ? 'Refresh' : 'Generate'} Data
                    </>
                  )}
                </Button>
                
                {selectedSchedule.has_geo_metadata && (
                  <Button onClick={downloadGeoMetadata} variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    Download JSON
                  </Button>
                )}
              </div>
            )}
          </div>

          {error && (
            <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <AlertCircle className="w-4 h-4 text-destructive" />
              <span className="text-sm text-destructive">{error}</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Selected Schedule Info */}
      {selectedSchedule && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Schedule Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Schedule ID</p>
                <p className="font-medium">{selectedSchedule.custom_id}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Project</p>
                <p className="font-medium">{selectedSchedule.project}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Client</p>
                <p className="font-medium">{selectedSchedule.client}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Status</p>
                <div className="flex items-center gap-2">
                  {selectedSchedule.has_geo_metadata ? (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-green-600 font-medium">Data Available</span>
                    </>
                  ) : (
                    <>
                      <Clock className="w-4 h-4 text-orange-600" />
                      <span className="text-orange-600 font-medium">No Data</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Geo Metadata Summary */}
      {locationSummary && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Location Summary</CardTitle>
            <CardDescription>
              Overview of geotagged files and their locations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <FileImage className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-blue-600">{locationSummary.photos}</p>
                <p className="text-sm text-muted-foreground">Photos</p>
              </div>
              <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <FileVideo className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-purple-600">{locationSummary.videos}</p>
                <p className="text-sm text-muted-foreground">Videos</p>
              </div>
              <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <MapPin className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-green-600">{locationSummary.coordinates.length}</p>
                <p className="text-sm text-muted-foreground">GPS Points</p>
              </div>
              <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <Camera className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-orange-600">{locationSummary.totalFiles}</p>
                <p className="text-sm text-muted-foreground">Total Files</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Center Point</h4>
                <p className="text-sm text-muted-foreground">
                  {locationSummary.center.lat.toFixed(6)}, {locationSummary.center.lng.toFixed(6)}
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">Coverage Area</h4>
                <p className="text-sm text-muted-foreground">
                  {((locationSummary.bounds.north - locationSummary.bounds.south) * 111).toFixed(2)} km × {((locationSummary.bounds.east - locationSummary.bounds.west) * 111).toFixed(2)} km
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* File List */}
      {loadingMetadata ? (
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">Loading geo metadata...</p>
            </div>
          </CardContent>
        </Card>
      ) : geoMetadata.length > 0 ? (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Geotagged Files</CardTitle>
            <CardDescription>
              List of files with GPS coordinates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {geoMetadata.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {item.type === 'photo' ? (
                      <Camera className="w-5 h-5 text-blue-600" />
                    ) : (
                      <Video className="w-5 h-5 text-purple-600" />
                    )}
                    <div>
                      <p className="font-medium">{item.filename}</p>
                      <p className="text-sm text-muted-foreground">
                        {item.type === 'photo' && item.lat && item.lng ? (
                          `${item.lat.toFixed(6)}, ${item.lng.toFixed(6)}`
                        ) : item.type === 'video' && item.start && item.end ? (
                          `${item.start.lat.toFixed(6)}, ${item.start.lng.toFixed(6)} → ${item.end.lat.toFixed(6)}, ${item.end.lng.toFixed(6)}`
                        ) : (
                          'No GPS data'
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={item.type === 'photo' ? 'default' : 'secondary'}>
                      {item.type}
                    </Badge>
                    {item.timestamp && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {new Date(item.timestamp).toLocaleString()}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ) : selectedSchedule && !selectedSchedule.has_geo_metadata ? (
        <Card>
          <CardContent className="text-center p-8">
            <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Geo Metadata Available</h3>
            <p className="text-muted-foreground mb-4">
              This schedule doesn't have geo metadata yet. Upload drone files and complete the "File Upload" task to generate heatmap data.
            </p>
            <Button onClick={triggerHeatmapAutomation} disabled={processingAutomation}>
              {processingAutomation ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Generate Heatmap Data
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      ) : null}
    </div>
  )
}