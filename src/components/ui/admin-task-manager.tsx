'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { tasksApi, usersApi, projectsApi } from '@/lib/api'
import { TaskAssignmentModal } from './task-assignment-modal'
import { Filter, Users, User, CheckSquare, Square } from 'lucide-react'
import toast from 'react-hot-toast'
import type { Task, User as UserType, Project } from '@/types'

interface AdminTaskManagerProps {
  currentUserRole: string
}

export function AdminTaskManager({ currentUserRole }: AdminTaskManagerProps) {
  const [tasks, setTasks] = useState<Task[]>([])
  const [users, setUsers] = useState<UserType[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedTasks, setSelectedTasks] = useState<string[]>([])
  const [showAssignmentModal, setShowAssignmentModal] = useState(false)
  
  // Filters
  const [filters, setFilters] = useState({
    assignedTo: '',
    status: '',
    priority: '',
    projectId: ''
  })

  const isAdminOrManager = currentUserRole === 'admin' || currentUserRole === 'manager'

  useEffect(() => {
    if (isAdminOrManager) {
      fetchData()
    }
  }, [isAdminOrManager])

  useEffect(() => {
    if (isAdminOrManager) {
      fetchTasks()
    }
  }, [filters, isAdminOrManager])

  const fetchData = async () => {
    try {
      const [usersData, projectsData] = await Promise.all([
        usersApi.getAll(),
        projectsApi.getAll()
      ])
      setUsers(usersData)
      setProjects(projectsData)
    } catch (error: any) {
      toast.error('Failed to load data')
      console.error('Error fetching data:', error)
    }
  }

  const fetchTasks = async () => {
    try {
      setLoading(true)
      const tasksData = await tasksApi.getAllWithFilters(filters)
      // Enhanced sorting: shoot tasks first, then project tasks
      const sortedTasks = tasksData.sort((a, b) => {
        const aIsProjectTask = !a.shoot_id
        const bIsProjectTask = !b.shoot_id

        // If one is project task and other is shoot task, shoot task comes first
        if (aIsProjectTask && !bIsProjectTask) return 1
        if (!aIsProjectTask && bIsProjectTask) return -1

        // Both are same type, sort by order field
        const aOrder = a.order ?? (aIsProjectTask ? 1000 : 0)
        const bOrder = b.order ?? (bIsProjectTask ? 1000 : 0)

        if (aOrder !== bOrder) {
          return aOrder - bOrder
        }

        // Fallback to creation time
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      })
      setTasks(sortedTasks)
    } catch (error: any) {
      toast.error('Failed to load tasks')
      console.error('Error fetching tasks:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSelectTask = (taskId: string) => {
    setSelectedTasks(prev => 
      prev.includes(taskId) 
        ? prev.filter(id => id !== taskId)
        : [...prev, taskId]
    )
  }

  const handleSelectAll = () => {
    if (selectedTasks.length === tasks.length) {
      setSelectedTasks([])
    } else {
      setSelectedTasks(tasks.map(task => task.id))
    }
  }

  const handleBulkAssign = () => {
    if (selectedTasks.length === 0) {
      toast.error('Please select tasks to assign')
      return
    }
    setShowAssignmentModal(true)
  }

  const getSelectedTasksData = () => {
    return tasks.filter(task => selectedTasks.includes(task.id))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-700 border-green-200'
      case 'in_progress':
        return 'bg-blue-100 text-blue-700 border-blue-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 'cancelled':
        return 'bg-red-100 text-red-700 border-red-200'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-700 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-700 border-orange-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-700 border-green-200'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  if (!isAdminOrManager) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Access denied. Only admin and manager users can access this page.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Task Management</h2>
        <div className="flex items-center space-x-3">
          {selectedTasks.length > 0 && (
            <Button
              onClick={handleBulkAssign}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Users className="w-4 h-4 mr-2" />
              Assign {selectedTasks.length} Task{selectedTasks.length > 1 ? 's' : ''}
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex items-center mb-3">
          <Filter className="w-4 h-4 mr-2" />
          <span className="font-medium">Filters</span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <Label htmlFor="assignee-filter" className="text-sm">Assigned To</Label>
            <select
              id="assignee-filter"
              value={filters.assignedTo}
              onChange={(e) => setFilters(prev => ({ ...prev, assignedTo: e.target.value }))}
              className="w-full mt-1 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
            >
              <option value="">All Users</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>{user.name}</option>
              ))}
            </select>
          </div>
          <div>
            <Label htmlFor="status-filter" className="text-sm">Status</Label>
            <select
              id="status-filter"
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="w-full mt-1 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div>
            <Label htmlFor="priority-filter" className="text-sm">Priority</Label>
            <select
              id="priority-filter"
              value={filters.priority}
              onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
              className="w-full mt-1 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
            >
              <option value="">All Priorities</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
          </div>
          <div>
            <Label htmlFor="project-filter" className="text-sm">Project</Label>
            <select
              id="project-filter"
              value={filters.projectId}
              onChange={(e) => setFilters(prev => ({ ...prev, projectId: e.target.value }))}
              className="w-full mt-1 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
            >
              <option value="">All Projects</option>
              {projects.map(project => (
                <option key={project.id} value={project.id}>{project.name}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Task List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={handleSelectAll}
                className="flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
              >
                {selectedTasks.length === tasks.length && tasks.length > 0 ? (
                  <CheckSquare className="w-4 h-4 mr-2" />
                ) : (
                  <Square className="w-4 h-4 mr-2" />
                )}
                Select All ({tasks.length})
              </button>
              {selectedTasks.length > 0 && (
                <span className="text-sm text-blue-600 dark:text-blue-400">
                  {selectedTasks.length} selected
                </span>
              )}
            </div>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <p className="text-gray-500">Loading tasks...</p>
          </div>
        ) : tasks.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No tasks found</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {tasks.map(task => (
              <div key={task.id} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    checked={selectedTasks.includes(task.id)}
                    onChange={() => handleSelectTask(task.id)}
                    className="mt-1"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                        {task.title}
                      </h4>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setSelectedTasks([task.id])
                          setShowAssignmentModal(true)
                        }}
                        className="ml-2"
                      >
                        <User className="w-3 h-3 mr-1" />
                        Assign
                      </Button>
                    </div>
                    <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                      <span>Project: {task.project?.name || 'N/A'}</span>
                      {task.shoot && <span>Shoot: {new Date(task.shoot.scheduled_date).toLocaleDateString()}</span>}
                      <span>Assigned to: {task.assigned_user?.name || 'Unassigned'}</span>
                    </div>
                    <div className="mt-2 flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                        {task.status.replace('_', ' ')}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)}`}>
                        {task.priority}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Assignment Modal */}
      <TaskAssignmentModal
        isOpen={showAssignmentModal}
        onClose={() => {
          setShowAssignmentModal(false)
          setSelectedTasks([])
        }}
        tasks={getSelectedTasksData()}
        onSuccess={() => {
          fetchTasks()
          setSelectedTasks([])
        }}
      />
    </div>
  )
}
