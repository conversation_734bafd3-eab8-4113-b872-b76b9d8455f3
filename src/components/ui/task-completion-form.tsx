'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Modal } from '@/components/ui/modal'
import { CheckCircle, Clock, FileText, MessageSquare } from 'lucide-react'
import type { Task } from '@/types'
import toast from 'react-hot-toast'

export interface TaskCompletionData {
  completion_notes?: string
  time_spent?: number // in minutes
  deliverables?: string
  next_steps?: string
}

interface TaskCompletionFormProps {
  task: Task | null
  isOpen: boolean
  onClose: () => void
  onComplete: (task: Task, completionData: TaskCompletionData) => void
}

export function TaskCompletionForm({ task, isOpen, onClose, onComplete }: TaskCompletionFormProps) {
  const { user } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState<TaskCompletionData>({
    completion_notes: '',
    time_spent: undefined,
    deliverables: '',
    next_steps: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!task || !user) return

    setIsSubmitting(true)
    try {
      await onComplete(task, formData)
      
      // Reset form
      setFormData({
        completion_notes: '',
        time_spent: undefined,
        deliverables: '',
        next_steps: ''
      })
      
      onClose()
      toast.success('Task completed successfully')
    } catch (error: any) {
      toast.error(error.message || 'Failed to complete task')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: keyof TaskCompletionData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  if (!task) return null

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Complete Task"
      size="lg"
    >
      <div className="space-y-6">
        {/* Task Info */}
        <div className="bg-muted/50 rounded-lg p-4">
          <h3 className="font-semibold text-lg mb-2">{task.title}</h3>
          {task.description && (
            <p className="text-muted-foreground text-sm">{task.description}</p>
          )}
          <div className="flex items-center gap-4 mt-3 text-sm text-muted-foreground">
            <span>Priority: <span className="capitalize">{task.priority}</span></span>
            {task.due_date && (
              <span>Due: {new Date(task.due_date).toLocaleDateString()}</span>
            )}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Completion Notes */}
          <div>
            <Label htmlFor="completion_notes" className="flex items-center gap-2">
              <MessageSquare className="w-4 h-4" />
              Completion Notes
            </Label>
            <Textarea
              id="completion_notes"
              placeholder="Describe what was accomplished, any issues encountered, or important details..."
              value={formData.completion_notes}
              onChange={(e) => handleInputChange('completion_notes', e.target.value)}
              rows={3}
              className="mt-1"
            />
          </div>

          {/* Time Spent */}
          <div>
            <Label htmlFor="time_spent" className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Time Spent (minutes)
            </Label>
            <Input
              id="time_spent"
              type="number"
              placeholder="e.g., 120"
              value={formData.time_spent || ''}
              onChange={(e) => handleInputChange('time_spent', parseInt(e.target.value) || 0)}
              className="mt-1"
              min="0"
            />
          </div>

          {/* Deliverables */}
          <div>
            <Label htmlFor="deliverables" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Deliverables / Files Created
            </Label>
            <Textarea
              id="deliverables"
              placeholder="List any files, documents, or deliverables created during this task..."
              value={formData.deliverables}
              onChange={(e) => handleInputChange('deliverables', e.target.value)}
              rows={2}
              className="mt-1"
            />
          </div>

          {/* Next Steps */}
          <div>
            <Label htmlFor="next_steps" className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4" />
              Next Steps / Follow-up
            </Label>
            <Textarea
              id="next_steps"
              placeholder="Any follow-up actions, next steps, or items for other team members..."
              value={formData.next_steps}
              onChange={(e) => handleInputChange('next_steps', e.target.value)}
              rows={2}
              className="mt-1"
            />
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting ? (
                <>
                  <Clock className="w-4 h-4 mr-2 animate-spin" />
                  Completing Task...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Complete Task
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  )
}
