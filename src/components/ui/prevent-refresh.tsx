'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { X, RefreshCw, Wifi } from 'lucide-react'
import { useRealtimeStatus } from '@/components/ui/realtime-indicator'

export function PreventRefresh() {
  const [showWarning, setShowWarning] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const { isConnected, isOnline } = useRealtimeStatus()

  useEffect(() => {
    // Prevent accidental refresh when there are unsaved changes
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault()
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return e.returnValue
      }
    }

    // Show warning when user tries to refresh
    const handleKeyDown = (e: KeyboardEvent) => {
      // Detect Ctrl+R, F5, or Cmd+R
      if (
        (e.ctrl<PERSON>ey && e.key === 'r') ||
        (e.metaKey && e.key === 'r') ||
        e.key === 'F5'
      ) {
        e.preventDefault()
        setShowWarning(true)
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('keydown', handleKeyDown)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [hasUnsavedChanges])

  // Auto-hide warning after 5 seconds
  useEffect(() => {
    if (showWarning) {
      const timer = setTimeout(() => {
        setShowWarning(false)
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [showWarning])

  // Listen for form changes to set unsaved changes flag
  useEffect(() => {
    const handleFormChange = () => {
      setHasUnsavedChanges(true)
    }

    const handleFormSubmit = () => {
      setHasUnsavedChanges(false)
    }

    // Listen for form inputs
    document.addEventListener('input', handleFormChange)
    document.addEventListener('submit', handleFormSubmit)

    return () => {
      document.removeEventListener('input', handleFormChange)
      document.removeEventListener('submit', handleFormSubmit)
    }
  }, [])

  const handleForceRefresh = () => {
    window.location.reload()
  }

  const handleDismiss = () => {
    setShowWarning(false)
  }

  if (!showWarning) return null

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-2">
            <RefreshCw className="w-5 h-5 text-blue-500" />
            <h3 className="font-semibold text-sm">Refresh Not Needed</h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="h-6 w-6 p-0"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="space-y-3">
          <p className="text-sm text-muted-foreground">
            This app updates automatically in real-time. No need to refresh!
          </p>
          
          <div className="flex items-center space-x-2 text-xs">
            <Wifi className={`w-3 h-3 ${isConnected && isOnline ? 'text-green-500' : 'text-red-500'}`} />
            <span className={isConnected && isOnline ? 'text-green-600' : 'text-red-600'}>
              {isConnected && isOnline ? 'Real-time updates active' : 'Connection issues detected'}
            </span>
          </div>
          
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleDismiss}
              className="flex-1 text-xs"
            >
              Got it
            </Button>
            {(!isConnected || !isOnline) && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleForceRefresh}
                className="flex-1 text-xs text-orange-600 hover:text-orange-700"
              >
                Force Refresh
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Hook to manage unsaved changes state
export function useUnsavedChanges() {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const markAsChanged = () => setHasUnsavedChanges(true)
  const markAsSaved = () => setHasUnsavedChanges(false)

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault()
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return e.returnValue
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  return {
    hasUnsavedChanges,
    markAsChanged,
    markAsSaved
  }
}
