'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { usersApi, tasksApi } from '@/lib/api'
import { X, User, Users } from 'lucide-react'
import toast from 'react-hot-toast'
import type { Task, User as UserType } from '@/types'

interface TaskAssignmentModalProps {
  isOpen: boolean
  onClose: () => void
  tasks: Task[] // Can be single task or multiple tasks
  onSuccess?: () => void
}

export function TaskAssignmentModal({ isOpen, onClose, tasks, onSuccess }: TaskAssignmentModalProps) {
  const [users, setUsers] = useState<UserType[]>([])
  const [selectedUserId, setSelectedUserId] = useState('')
  const [loading, setLoading] = useState(false)
  const [usersLoading, setUsersLoading] = useState(false)

  const isBulkAssignment = tasks.length > 1

  useEffect(() => {
    if (isOpen) {
      fetchUsers()
      // Pre-select current assignee if single task
      if (!isBulkAssignment && tasks[0]?.assigned_user?.id) {
        setSelectedUserId(tasks[0].assigned_user.id)
      } else {
        setSelectedUserId('')
      }
    }
  }, [isOpen, tasks, isBulkAssignment])

  const fetchUsers = async () => {
    try {
      setUsersLoading(true)
      const allUsers = await usersApi.getAll()
      setUsers(allUsers)
    } catch (error: any) {
      toast.error('Failed to load users')
      console.error('Error fetching users:', error)
    } finally {
      setUsersLoading(false)
    }
  }

  const handleAssign = async () => {
    if (!selectedUserId) {
      toast.error('Please select a user to assign the task(s) to')
      return
    }

    try {
      setLoading(true)

      if (isBulkAssignment) {
        // Bulk assignment
        const taskIds = tasks.map(task => task.id)
        await tasksApi.bulkAssignTasks(taskIds, selectedUserId)
        toast.success(`${tasks.length} tasks assigned successfully`)
      } else {
        // Single task assignment
        await tasksApi.assignTask(tasks[0].id, selectedUserId)
        toast.success('Task assigned successfully')
      }

      onSuccess?.()
      onClose()
    } catch (error: any) {
      toast.error(error.message || 'Failed to assign task(s)')
      console.error('Error assigning tasks:', error)
    } finally {
      setLoading(false)
    }
  }

  const getUserRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-700 border-red-200'
      case 'manager':
        return 'bg-blue-100 text-blue-700 border-blue-200'
      case 'pilot':
        return 'bg-green-100 text-green-700 border-green-200'
      case 'editor':
        return 'bg-purple-100 text-purple-700 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold flex items-center">
            {isBulkAssignment ? <Users className="w-5 h-5 mr-2" /> : <User className="w-5 h-5 mr-2" />}
            {isBulkAssignment ? `Assign ${tasks.length} Tasks` : 'Assign Task'}
          </h3>
          <Button
            variant="outline"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Task Info */}
        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          {isBulkAssignment ? (
            <div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Selected Tasks:
              </p>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {tasks.map(task => (
                  <div key={task.id} className="text-xs text-gray-600 dark:text-gray-400">
                    • {task.title}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Task: {tasks[0]?.title}
              </p>
              {tasks[0]?.assigned_user && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Currently assigned to: {tasks[0].assigned_user.name}
                </p>
              )}
            </div>
          )}
        </div>

        {/* User Selection */}
        <div className="mb-6">
          <Label htmlFor="user-select" className="text-sm font-medium mb-2 block">
            Assign to User:
          </Label>
          
          {usersLoading ? (
            <div className="text-center py-4 text-gray-500">Loading users...</div>
          ) : (
            <div className="space-y-2 max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg p-2">
              {users.map(user => (
                <label
                  key={user.id}
                  className={`flex items-center p-2 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${
                    selectedUserId === user.id ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700' : ''
                  }`}
                >
                  <input
                    type="radio"
                    name="assignee"
                    value={user.id}
                    checked={selectedUserId === user.id}
                    onChange={(e) => setSelectedUserId(e.target.value)}
                    className="mr-3"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {user.name}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getUserRoleColor(user.role)}`}>
                        {user.role}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {user.email}
                    </span>
                  </div>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleAssign}
            disabled={loading || !selectedUserId || usersLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {loading ? 'Assigning...' : isBulkAssignment ? `Assign ${tasks.length} Tasks` : 'Assign Task'}
          </Button>
        </div>
      </div>
    </div>
  )
}
