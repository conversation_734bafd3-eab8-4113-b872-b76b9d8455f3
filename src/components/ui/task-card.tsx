'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { TaskAssignmentModal } from './task-assignment-modal'
import { formatDate } from '@/lib/utils'
import { createClientSupabaseClient } from '@/lib/auth'
import {
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Circle,
  Play,
  User,
  Calendar,
  Building,
  Camera,
  Edit,
  Trash2,
  Flag,
  Target,
  UserPlus
} from 'lucide-react'
import type { Task, Shoot } from '@/types'

interface TaskCardProps {
  task: Task
  onEdit?: (task: Task) => void
  onDelete?: (task: Task) => void
  onStatusChange?: (task: Task, newStatus: Task['status']) => void
  onCompleteSchedule?: (schedule: Schedule) => void
  onTaskAssigned?: () => void
  schedule?: Schedule
  compact?: boolean
}

export function TaskCard({ task, onEdit, onDelete, onStatusChange, onCompleteSchedule, onTaskAssigned, schedule, compact = false }: TaskCardProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [showAssignmentModal, setShowAssignmentModal] = useState(false)
  const [currentUserRole, setCurrentUserRole] = useState<string>('')

  useEffect(() => {
    getCurrentUserRole()
  }, [])

  const getCurrentUserRole = async () => {
    try {
      const supabase = createClientSupabaseClient()
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) return

      const { data: userProfile } = await supabase
        .from('users')
        .select('role')
        .eq('id', user.id)
        .single()

      setCurrentUserRole(userProfile?.role || '')
    } catch (error) {
      console.error('Error getting current user role:', error)
    }
  }

  const isAdminOrManager = currentUserRole === 'admin' || currentUserRole === 'manager'

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Circle className="w-4 h-4" />
      case 'in_progress':
        return <Play className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'cancelled':
        return <XCircle className="w-4 h-4" />
      default:
        return <Circle className="w-4 h-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-muted text-muted-foreground border-border'
      case 'in_progress':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-800'
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800'
      case 'cancelled':
        return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800'
      default:
        return 'bg-muted text-muted-foreground border-border'
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <AlertTriangle className="w-4 h-4" />
      case 'high':
        return <Flag className="w-4 h-4" />
      case 'medium':
        return <Flag className="w-4 h-4" />
      case 'low':
        return <Flag className="w-4 h-4" />
      default:
        return <Flag className="w-4 h-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600'
      case 'high':
        return 'text-orange-600'
      case 'medium':
        return 'text-yellow-600'
      case 'low':
        return 'text-green-600'
      default:
        return 'text-muted-foreground'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pending'
      case 'in_progress':
        return 'In Progress'
      case 'completed':
        return 'Completed'
      case 'cancelled':
        return 'Cancelled'
      default:
        return status
    }
  }

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'Urgent'
      case 'high':
        return 'High'
      case 'medium':
        return 'Medium'
      case 'low':
        return 'Low'
      default:
        return priority
    }
  }

  const isOverdue = task.due_date && new Date(task.due_date) < new Date() && task.status !== 'completed'

  const handleStatusChange = async (newStatus: Task['status']) => {
    setIsUpdatingStatus(true)
    try {
      // Update the task with timestamps
      const updates: Partial<Task> = { status: newStatus }

      if (newStatus === 'in_progress' && task.status === 'pending') {
        updates.started_at = new Date().toISOString()
      } else if (newStatus === 'completed' && task.status === 'in_progress') {
        updates.completed_at = new Date().toISOString()
      }

      await onStatusChange?.(task, newStatus)
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete the task "${task.title}"? This action cannot be undone.`)) {
      return
    }

    setIsDeleting(true)
    try {
      await onDelete?.(task)
    } finally {
      setIsDeleting(false)
    }
  }

  if (compact) {
    return (
      <div className={`app-card p-4 transition-all duration-200 ${isOverdue ? 'border-red-300 bg-red-50 dark:bg-red-900/10' : ''}`}>
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                {getStatusIcon(task.status)}
                <span className="ml-1">{getStatusLabel(task.status)}</span>
              </div>
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                {getPriorityIcon(task.priority)}
                <span className="ml-1">{getPriorityLabel(task.priority)}</span>
              </div>
            </div>
            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-1 truncate">{task.title}</h4>
            {task.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-1 mb-2">{task.description}</p>
            )}
            <div className="flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400">
              {task.due_date && (
                <div className={`flex items-center ${isOverdue ? 'text-red-600 dark:text-red-400' : ''}`}>
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatDate(task.due_date)}
                </div>
              )}
              {task.assigned_user && (
                <div className="flex items-center">
                  <User className="w-3 h-3 mr-1" />
                  {task.assigned_user.name}
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-1 ml-3">
            {/* Complete Schedule Button */}
            {schedule && schedule.status !== 'completed' && onCompleteSchedule && task.title.toLowerCase() === 'shoot' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onCompleteSchedule(schedule)}
                className="h-8 w-8 p-0 hover:bg-green-50 dark:hover:bg-green-900/20"
                title="Complete Schedule"
              >
                <Target className="w-4 h-4 text-green-600" />
              </Button>
            )}
            {task.status !== 'completed' && task.status !== 'cancelled' && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleStatusChange(task.status === 'pending' ? 'in_progress' : 'completed')}
                  disabled={isUpdatingStatus}
                  className="h-8 w-8 p-0 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  title={task.status === 'pending' ? 'Start Task' : 'Complete Task'}
                >
                  {task.status === 'pending' ? (
                    <Play className="w-4 h-4 text-blue-600" />
                  ) : (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  )}
                </Button>
                {/* Skip Task Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleStatusChange('skipped')}
                  disabled={isUpdatingStatus}
                  className="h-8 w-8 p-0 hover:bg-yellow-50 dark:hover:bg-yellow-900/20"
                  title="Skip Task"
                >
                  <XCircle className="w-4 h-4 text-yellow-600" />
                </Button>
              </>
            )}
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(task)}
                className="h-8 w-8 p-0 hover:bg-gray-50 dark:hover:bg-gray-700"
                title="Edit Task"
              >
                <Edit className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              </Button>
            )}
            {onDelete && isAdminOrManager && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDelete}
                disabled={isDeleting}
                className="h-8 w-8 p-0 hover:bg-red-50 dark:hover:bg-red-900/20"
                title="Delete Task"
              >
                <Trash2 className="w-4 h-4 text-red-600 dark:text-red-400" />
              </Button>
            )}
          </div>
        </div>

        {/* Task Assignment Modal */}
        <TaskAssignmentModal
          isOpen={showAssignmentModal}
          onClose={() => setShowAssignmentModal(false)}
          tasks={[task]}
          onSuccess={() => {
            onTaskAssigned?.()
            setShowAssignmentModal(false)
          }}
        />
      </div>
    )
  }

  return (
    <div className={`app-card shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden ${isOverdue ? 'border-l-4 border-red-500' : ''}`}>
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-3">
              <div className={`flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                {getStatusIcon(task.status)}
                <span className="ml-1">{getStatusLabel(task.status)}</span>
              </div>
              <div className={`flex items-center px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                {getPriorityIcon(task.priority)}
                <span className="ml-1">{getPriorityLabel(task.priority)}</span>
              </div>
              {isOverdue && (
                <div className="flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border border-red-200 dark:border-red-800">
                  <Clock className="w-3 h-3 mr-1" />
                  <span>Overdue</span>
                </div>
              )}
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
              {task.title}
            </h3>
            {task.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-4">{task.description}</p>
            )}
          </div>
          <div className="flex items-center space-x-2 ml-4">
            {/* Complete Schedule Button */}
            {schedule && schedule.status !== 'completed' && onCompleteSchedule && task.title.toLowerCase() === 'shoot' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCompleteSchedule(schedule)}
                className="text-green-600 hover:text-green-700 border-green-200 hover:border-green-300"
              >
                <Target className="w-4 h-4 mr-2" />
                Complete Schedule
              </Button>
            )}
            {task.status !== 'completed' && task.status !== 'cancelled' && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStatusChange(task.status === 'pending' ? 'in_progress' : 'completed')}
                  disabled={isUpdatingStatus}
                  className="text-sm"
                >
                  {task.status === 'pending' ? (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Start Task
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Complete
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStatusChange('skipped')}
                  disabled={isUpdatingStatus}
                  className="text-sm text-yellow-600 hover:text-yellow-700 border-yellow-200 hover:border-yellow-300"
                >
                  <XCircle className="w-4 h-4 mr-2" />
                  Skip
                </Button>
              </>
            )}
            {onEdit && (
              <Button variant="outline" size="sm" onClick={() => onEdit(task)}>
                <Edit className="w-4 h-4" />
              </Button>
            )}
            {onDelete && isAdminOrManager && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                disabled={isDeleting}
                className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>

        {/* Task Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
          {task.due_date && (
            <div className={`flex items-center ${isOverdue ? 'text-red-600 dark:text-red-400' : 'text-gray-600 dark:text-gray-400'}`}>
              <Calendar className="w-4 h-4 mr-2" />
              <span className="font-medium">Due:</span>
              <span className="ml-2">{formatDate(task.due_date)}</span>
            </div>
          )}

          {task.assigned_user && (
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <User className="w-4 h-4 mr-2" />
              <span className="font-medium">Assigned:</span>
              <span className="ml-2">{task.assigned_user.name}</span>
            </div>
          )}

          {task.project && (
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <Building className="w-4 h-4 mr-2" />
              <span className="font-medium">Project:</span>
              <span className="ml-2">{task.project.name}</span>
            </div>
          )}

          {task.shoot && (
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <Camera className="w-4 h-4 mr-2" />
              <span className="font-medium">Shoot:</span>
              <span className="ml-2">{task.shoot.title}</span>
            </div>
          )}

          {task.started_at && (
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <Play className="w-4 h-4 mr-2" />
              <span className="font-medium">Started:</span>
              <span className="ml-2">{formatDate(task.started_at)}</span>
            </div>
          )}

          {task.completed_at && (
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <CheckCircle className="w-4 h-4 mr-2" />
              <span className="font-medium">Completed:</span>
              <span className="ml-2">{formatDate(task.completed_at)}</span>
            </div>
          )}
        </div>
      </div>

      {/* Assignment Modal */}
      <TaskAssignmentModal
        isOpen={showAssignmentModal}
        onClose={() => setShowAssignmentModal(false)}
        tasks={[task]}
        onSuccess={() => {
          onTaskAssigned?.()
          setShowAssignmentModal(false)
        }}
      />
    </div>
  )
}
