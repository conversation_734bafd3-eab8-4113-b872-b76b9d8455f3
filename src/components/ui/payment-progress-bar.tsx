import React from 'react'

interface PaymentProgressBarProps {
  status: 'pending' | 'paid' | 'overdue' | 'cancelled'
  totalAmount: number
  paidAmount?: number
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function PaymentProgressBar({ 
  status, 
  totalAmount, 
  paidAmount, 
  size = 'md',
  className = '' 
}: PaymentProgressBarProps) {
  // Calculate progress percentage
  const getProgress = () => {
    if (status === 'paid') return 100
    if (status === 'cancelled') return 0
    if (paidAmount && paidAmount > 0) {
      return Math.min((paidAmount / totalAmount) * 100, 100)
    }
    return 0
  }

  // Get color based on status
  const getProgressColor = () => {
    switch (status) {
      case 'paid':
        return 'bg-green-500'
      case 'overdue':
        return 'bg-red-500'
      case 'cancelled':
        return 'bg-gray-400'
      default: // pending
        return 'bg-yellow-500'
    }
  }

  // Get background color
  const getBackgroundColor = () => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 dark:bg-green-900/20'
      case 'overdue':
        return 'bg-red-100 dark:bg-red-900/20'
      case 'cancelled':
        return 'bg-gray-100 dark:bg-gray-900/20'
      default: // pending
        return 'bg-yellow-100 dark:bg-yellow-900/20'
    }
  }

  const progress = getProgress()
  const progressColor = getProgressColor()
  const backgroundColor = getBackgroundColor()

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          bar: 'h-2',
          text: 'text-xs',
          spacing: 'space-y-1'
        }
      case 'lg':
        return {
          bar: 'h-4',
          text: 'text-base',
          spacing: 'space-y-3'
        }
      default: // md
        return {
          bar: 'h-3',
          text: 'text-sm',
          spacing: 'space-y-2'
        }
    }
  }

  const sizeClasses = getSizeClasses()

  return (
    <div className={`${sizeClasses.spacing} ${className}`}>
      {/* Progress Bar */}
      <div className={`w-full ${sizeClasses.bar} rounded-full ${backgroundColor} overflow-hidden`}>
        <div 
          className={`h-full ${progressColor} transition-all duration-300 ease-in-out rounded-full`}
          style={{ width: `${progress}%` }}
        />
      </div>
      
      {/* Status and Amount */}
      <div className={`flex items-center justify-between ${sizeClasses.text}`}>
        <span className={`font-medium ${
          status === 'paid' 
            ? 'text-green-700 dark:text-green-400'
            : status === 'overdue'
            ? 'text-red-700 dark:text-red-400'
            : status === 'cancelled'
            ? 'text-gray-700 dark:text-gray-400'
            : 'text-yellow-700 dark:text-yellow-400'
        }`}>
          {status === 'paid' ? 'Paid' : 
           status === 'overdue' ? 'Overdue' :
           status === 'cancelled' ? 'Cancelled' :
           'Pending'}
        </span>
        <span className="text-muted-foreground">
          {progress.toFixed(0)}%
        </span>
      </div>
      
      {/* Amount Details */}
      <div className={`${size === 'sm' ? 'text-xs' : 'text-xs'} text-muted-foreground`}>
        {paidAmount && paidAmount > 0 && paidAmount !== totalAmount ? (
          <span>₹{paidAmount.toLocaleString()} of ₹{totalAmount.toLocaleString()}</span>
        ) : (
          <span>₹{totalAmount.toLocaleString()}</span>
        )}
      </div>
    </div>
  )
}