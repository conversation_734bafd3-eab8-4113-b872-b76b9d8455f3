import { useState, useEffect, useCallback } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import { projectsApi } from '@/lib/api'
import type { Project } from '@/types'

interface UseRealTimeProjectsOptions {
  clientId?: string
  status?: string
  onProjectCreate?: (project: Project) => void
  onProjectUpdate?: (project: Project) => void
  onProjectDelete?: (projectId: string) => void
}

export function useRealTimeProjects(options: UseRealTimeProjectsOptions = {}) {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientSupabaseClient()

  const { clientId, status, onProjectCreate, onProjectUpdate, onProjectDelete } = options

  // Fetch projects
  const fetchProjects = useCallback(async () => {
    try {
      setError(null)
      console.log('Fetching projects with filters:', { clientId, status })
      
      let data = await projectsApi.getAll()
      
      // Apply client-side filters
      if (clientId) {
        data = data.filter(project => project.client_id === clientId)
      }
      if (status) {
        data = data.filter(project => project.status === status)
      }
      
      setProjects(data)
      console.log(`Loaded ${data.length} projects`)
    } catch (err) {
      console.error('Error fetching projects:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch projects')
    } finally {
      setLoading(false)
    }
  }, [clientId, status])

  // Optimistic update function
  const updateProjectOptimistically = useCallback((projectId: string, updates: Partial<Project>) => {
    setProjects(prev => 
      prev.map(project => 
        project.id === projectId 
          ? { ...project, ...updates, updated_at: new Date().toISOString() }
          : project
      )
    )
  }, [])

  // Set up real-time subscriptions
  useEffect(() => {
    fetchProjects()

    // Create channel for real-time updates
    const channel = supabase
      .channel('projects-realtime')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'projects'
        },
        async (payload) => {
          console.log('Project created:', payload)
          
          // Fetch the complete project with relations
          try {
            const { data: newProject, error } = await supabase
              .from('projects')
              .select(`
                id, custom_id, name, description, client_id, location, status, total_amount, amount_received, amount_pending, vendor_payment_status, created_at, updated_at,
                client:clients(id, custom_id, name, client_type),
                contact_person:contact_persons(id, name, phone, email),
                schedules:schedules(count)
              `)
              .eq('id', payload.new.id)
              .single()

            if (!error && newProject) {
              const project = newProject as unknown as Project
              
              // Apply filters before adding
              const shouldInclude = (!clientId || project.client_id === clientId) &&
                                  (!status || project.status === status)
              
              if (shouldInclude) {
                setProjects(prev => [project, ...prev])
                onProjectCreate?.(project)
              }
            }
          } catch (error) {
            console.error('Error fetching new project details:', error)
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'projects'
        },
        async (payload) => {
          console.log('Project updated:', payload)
          
          // Fetch the complete updated project with relations
          try {
            const { data: updatedProject, error } = await supabase
              .from('projects')
              .select(`
                id, custom_id, name, description, client_id, location, status, total_amount, amount_received, amount_pending, vendor_payment_status, created_at, updated_at,
                client:clients(id, custom_id, name, client_type),
                contact_person:contact_persons(id, name, phone, email),
                schedules:schedules(count)
              `)
              .eq('id', payload.new.id)
              .single()

            if (!error && updatedProject) {
              const project = updatedProject as unknown as Project
              
              setProjects(prev => {
                const exists = prev.find(p => p.id === project.id)
                const shouldInclude = (!clientId || project.client_id === clientId) &&
                                    (!status || project.status === status)
                
                if (exists && shouldInclude) {
                  // Update existing project
                  return prev.map(p => p.id === project.id ? project : p)
                } else if (!exists && shouldInclude) {
                  // Add new project (status changed to match filter)
                  return [project, ...prev]
                } else if (exists && !shouldInclude) {
                  // Remove project (no longer matches filter)
                  return prev.filter(p => p.id !== project.id)
                }
                
                return prev
              })
              
              onProjectUpdate?.(project)
            }
          } catch (error) {
            console.error('Error fetching updated project details:', error)
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'projects'
        },
        (payload) => {
          console.log('Project deleted:', payload)
          const deletedProjectId = payload.old.id
          
          setProjects(prev => prev.filter(p => p.id !== deletedProjectId))
          onProjectDelete?.(deletedProjectId)
        }
      )
      .subscribe()

    // Cleanup subscription on unmount
    return () => {
      console.log('Unsubscribing from projects real-time updates')
      supabase.removeChannel(channel)
    }
  }, [supabase, fetchProjects, clientId, status, onProjectCreate, onProjectUpdate, onProjectDelete])

  // Manual refresh function
  const refresh = useCallback(() => {
    setLoading(true)
    fetchProjects()
  }, [fetchProjects])

  return {
    projects,
    loading,
    error,
    refresh,
    updateProjectOptimistically
  }
}
