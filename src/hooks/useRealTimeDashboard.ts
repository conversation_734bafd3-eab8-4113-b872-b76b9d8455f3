import { useEffect, useState, useCallback } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import { dashboardApi } from '@/lib/api'
import type { DashboardStats } from '@/types'

export function useRealTimeDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientSupabaseClient()

  // Fetch dashboard stats
  const fetchStats = useCallback(async () => {
    try {
      setError(null)
      const dashboardStats = await dashboardApi.getStats()
      setStats(dashboardStats)
    } catch (err) {
      console.error('Error fetching dashboard stats:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard stats')
    } finally {
      setLoading(false)
    }
  }, [])

  // Set up real-time subscriptions
  useEffect(() => {
    fetchStats()

    // Create channel for real-time updates
    const channel = supabase
      .channel('dashboard-realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'projects'
        },
        () => {
          console.log('Project data changed, refreshing dashboard stats')
          fetchStats()
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'schedules'
        },
        () => {
          console.log('Schedule data changed, refreshing dashboard stats')
          fetchStats()
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tasks'
        },
        () => {
          console.log('Task data changed, refreshing dashboard stats')
          fetchStats()
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'payments'
        },
        () => {
          console.log('Payment data changed, refreshing dashboard stats')
          fetchStats()
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'expenses'
        },
        () => {
          console.log('Expense data changed, refreshing dashboard stats')
          fetchStats()
        }
      )
      .subscribe()

    // Cleanup subscription on unmount
    return () => {
      console.log('Unsubscribing from dashboard real-time updates')
      supabase.removeChannel(channel)
    }
  }, [supabase, fetchStats])

  // Manual refresh function
  const refresh = useCallback(() => {
    setLoading(true)
    fetchStats()
  }, [fetchStats])

  return {
    stats,
    loading,
    error,
    refresh
  }
}
