import { useEffect, useState, useCallback } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import type { Task } from '@/types'

interface UseRealTimeTasksOptions {
  projectId?: string
  shootId?: string
  assignedTo?: string
  onTaskUpdate?: (task: Task) => void
  onTaskCreate?: (task: Task) => void
  onTaskDelete?: (taskId: string) => void
}

export function useRealTimeTasks(options: UseRealTimeTasksOptions = {}) {
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientSupabaseClient()

  const {
    projectId,
    shootId,
    assignedTo,
    onTaskUpdate,
    onTaskCreate,
    onTaskDelete
  } = options

  // Fetch initial tasks
  const fetchTasks = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      let query = supabase
        .from('tasks')
        .select(`
          *,
          assigned_user:users(id, name, email, role),
          project:projects(id, custom_id, name, status),
          shoot:schedules(id, custom_id, scheduled_date)
        `)

      // Apply filters
      if (projectId) {
        query = query.eq('project_id', projectId)
      }
      if (shootId) {
        query = query.eq('shoot_id', shootId)
      }
      if (assignedTo) {
        query = query.eq('assigned_to', assignedTo)
      }

      query = query.order('created_at', { ascending: false })

      const { data, error: fetchError } = await query

      if (fetchError) {
        throw new Error(fetchError.message)
      }

      setTasks((data as unknown as Task[]) || [])
    } catch (err) {
      console.error('Error fetching tasks:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch tasks')
    } finally {
      setLoading(false)
    }
  }, [supabase, projectId, shootId, assignedTo])

  // Set up real-time subscription
  useEffect(() => {
    fetchTasks()

    // Create channel for real-time updates
    const channel = supabase
      .channel('tasks-realtime')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'tasks'
        },
        async (payload) => {
          console.log('Task created:', payload)
          
          // Fetch the complete task with relations
          const { data: newTask, error } = await supabase
            .from('tasks')
            .select(`
              *,
              assigned_user:users(id, name, email, role),
              project:projects(id, custom_id, name, status),
              shoot:schedules(id, custom_id, scheduled_date)
            `)
            .eq('id', payload.new.id)
            .single()

          if (!error && newTask) {
            const task = newTask as unknown as Task
            
            // Check if task matches our filters
            const matchesFilters = (
              (!projectId || task.project_id === projectId) &&
              (!shootId || task.shoot_id === shootId) &&
              (!assignedTo || task.assigned_to === assignedTo)
            )

            if (matchesFilters) {
              setTasks(prev => [task, ...prev])
              onTaskCreate?.(task)
            }
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'tasks'
        },
        async (payload) => {
          console.log('Task updated:', payload)
          
          // Fetch the complete updated task with relations
          const { data: updatedTask, error } = await supabase
            .from('tasks')
            .select(`
              *,
              assigned_user:users(id, name, email, role),
              project:projects(id, custom_id, name, status),
              shoot:schedules(id, custom_id, scheduled_date)
            `)
            .eq('id', payload.new.id)
            .single()

          if (!error && updatedTask) {
            const task = updatedTask as unknown as Task
            
            setTasks(prev => 
              prev.map(t => t.id === task.id ? task : t)
            )
            onTaskUpdate?.(task)
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'tasks'
        },
        (payload) => {
          console.log('Task deleted:', payload)
          const deletedTaskId = payload.old.id
          
          setTasks(prev => prev.filter(t => t.id !== deletedTaskId))
          onTaskDelete?.(deletedTaskId)
        }
      )
      .subscribe()

    // Cleanup subscription on unmount
    return () => {
      console.log('Unsubscribing from tasks real-time updates')
      supabase.removeChannel(channel)
    }
  }, [supabase, fetchTasks, projectId, shootId, assignedTo, onTaskCreate, onTaskUpdate, onTaskDelete])

  // Manual refresh function
  const refresh = useCallback(() => {
    fetchTasks()
  }, [fetchTasks])

  // Update task optimistically
  const updateTaskOptimistically = useCallback((taskId: string, updates: Partial<Task>) => {
    setTasks(prev => 
      prev.map(task => 
        task.id === taskId 
          ? { ...task, ...updates, updated_at: new Date().toISOString() }
          : task
      )
    )
  }, [])

  // Add task optimistically
  const addTaskOptimistically = useCallback((task: Task) => {
    setTasks(prev => [task, ...prev])
  }, [])

  // Remove task optimistically
  const removeTaskOptimistically = useCallback((taskId: string) => {
    setTasks(prev => prev.filter(t => t.id !== taskId))
  }, [])

  return {
    tasks,
    loading,
    error,
    refresh,
    updateTaskOptimistically,
    addTaskOptimistically,
    removeTaskOptimistically
  }
}
