import { useState, useEffect, useCallback } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import type { 
  Notification, 
  NotificationFilters, 
  NotificationStats,
  CreateNotificationRequest 
} from '@/types/notifications'

export function useNotifications(filters?: NotificationFilters) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [stats, setStats] = useState<NotificationStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientSupabaseClient()

  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      if (filters?.read !== undefined) params.set('read', filters.read.toString())
      if (filters?.type) params.set('type', filters.type)
      if (filters?.category) params.set('category', filters.category)
      if (filters?.priority) params.set('priority', filters.priority)
      if (filters?.limit) params.set('limit', filters.limit.toString())
      if (filters?.offset) params.set('offset', filters.offset.toString())

      const response = await fetch(`/api/notifications?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch notifications')
      }

      const data = await response.json()
      setNotifications(data)
    } catch (err) {
      console.error('Error fetching notifications:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch notifications')
    } finally {
      setLoading(false)
    }
  }, [filters])

  // Fetch stats
  const fetchStats = useCallback(async () => {
    try {
      const supabase = createClientSupabaseClient()
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        console.warn('No session found for notification stats')
        return
      }

      const response = await fetch('/api/notifications/stats', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        throw new Error('Failed to fetch notification stats')
      }

      const data = await response.json()
      setStats(data)
    } catch (err) {
      console.error('Error fetching notification stats:', err)
    }
  }, [])

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ read: true })
      })

      if (!response.ok) {
        throw new Error('Failed to mark notification as read')
      }

      // Update local state
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId 
            ? { ...n, read: true, read_at: new Date().toISOString() }
            : n
        )
      )

      // Update stats
      setStats(prev => prev ? { ...prev, unread: prev.unread - 1 } : null)
    } catch (err) {
      console.error('Error marking notification as read:', err)
      throw err
    }
  }, [])

  // Mark notification as unread
  const markAsUnread = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ read: false, read_at: null })
      })

      if (!response.ok) {
        throw new Error('Failed to mark notification as unread')
      }

      // Update local state
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId 
            ? { ...n, read: false, read_at: undefined }
            : n
        )
      )

      // Update stats
      setStats(prev => prev ? { ...prev, unread: prev.unread + 1 } : null)
    } catch (err) {
      console.error('Error marking notification as unread:', err)
      throw err
    }
  }, [])

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete notification')
      }

      // Update local state
      const deletedNotification = notifications.find(n => n.id === notificationId)
      setNotifications(prev => prev.filter(n => n.id !== notificationId))

      // Update stats
      setStats(prev => {
        if (!prev) return null
        return {
          ...prev,
          total: prev.total - 1,
          unread: deletedNotification && !deletedNotification.read 
            ? prev.unread - 1 
            : prev.unread
        }
      })
    } catch (err) {
      console.error('Error deleting notification:', err)
      throw err
    }
  }, [notifications])

  // Bulk actions
  const bulkAction = useCallback(async (
    action: 'mark_read' | 'mark_unread' | 'delete',
    notificationIds?: string[],
    actionFilters?: NotificationFilters
  ) => {
    try {
      const response = await fetch('/api/notifications/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          notification_ids: notificationIds,
          filters: actionFilters
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to perform bulk ${action}`)
      }

      // Refresh data after bulk action
      await Promise.all([fetchNotifications(), fetchStats()])
    } catch (err) {
      console.error(`Error performing bulk ${action}:`, err)
      throw err
    }
  }, [fetchNotifications, fetchStats])

  // Create notification (for admin/system use)
  const createNotification = useCallback(async (data: CreateNotificationRequest) => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error('Failed to create notification')
      }

      const notification = await response.json()
      
      // Add to local state if it's for the current user
      setNotifications(prev => [notification, ...prev])
      setStats(prev => prev ? { 
        ...prev, 
        total: prev.total + 1,
        unread: prev.unread + 1
      } : null)

      return notification
    } catch (err) {
      console.error('Error creating notification:', err)
      throw err
    }
  }, [])

  // Set up real-time subscriptions
  useEffect(() => {
    fetchNotifications()
    fetchStats()

    // Subscribe to real-time updates
    const channel = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications'
        },
        (payload) => {
          const newNotification = payload.new as Notification
          setNotifications(prev => [newNotification, ...prev])
          setStats(prev => prev ? {
            ...prev,
            total: prev.total + 1,
            unread: prev.unread + 1
          } : null)
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'notifications'
        },
        (payload) => {
          const updatedNotification = payload.new as Notification
          setNotifications(prev => 
            prev.map(n => n.id === updatedNotification.id ? updatedNotification : n)
          )
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'notifications'
        },
        (payload) => {
          const deletedId = payload.old.id
          setNotifications(prev => prev.filter(n => n.id !== deletedId))
          setStats(prev => prev ? {
            ...prev,
            total: prev.total - 1,
            unread: payload.old.read ? prev.unread : prev.unread - 1
          } : null)
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [supabase, fetchNotifications, fetchStats])

  return {
    notifications,
    stats,
    loading,
    error,
    markAsRead,
    markAsUnread,
    deleteNotification,
    bulkAction,
    createNotification,
    refresh: fetchNotifications
  }
}
