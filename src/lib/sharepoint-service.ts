import { createScheduleFolder } from '@/lib/microsoft-graph'
import { createClient } from '@supabase/supabase-js'

// Helper function to create server-side Supabase client with proper error handling
function createServerSupabaseClient() {
  console.log('--- ENV VARS IN SHAREPOINT-SERVICE ---');
  console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
  console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY);
  console.log('------------------------------------');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_URL is missing in sharepoint-service.ts background job context');
    throw new Error('NEXT_PUBLIC_SUPABASE_URL is required')
  }

  if (!supabaseServiceKey) {
    console.error('❌ SUPABASE_SERVICE_ROLE_KEY is missing in sharepoint-service.ts background job context');
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is required')
  }

  return createClient(supabaseUrl, supabaseServiceKey)
}

export class SharePointService {
  /**
   * Ensures SharePoint folder is created for a specific schedule
   */
  static async ensureScheduleFolder(scheduleId: string): Promise<boolean> {
    try {
      console.log('🔄 Ensuring SharePoint folder for schedule:', scheduleId)
      
      // Get schedule details with project and client info using server client
      const serverSupabase = createServerSupabaseClient()
      const { data: schedule, error } = await serverSupabase
        .from('schedules')
        .select(`
          id,
          custom_id,
          project_id,
          scheduled_date,
          scheduled_end_date,
          projects!inner (
            id,
            custom_id,
            name,
            client_id,
            clients!inner (
              id,
              custom_id,
              name,
              sharepoint_folder_id
            )
          )
        `)
        .eq('id', scheduleId)
        .single()

      if (error || !schedule) {
        console.log('❌ Schedule not found:', scheduleId, error)
        return false
      }

      // Type assertion to help TypeScript understand the structure
      const project = schedule.projects as any
      const client = project.clients as any

      if (!project || !client) {
        console.log('❌ Project or client not found for schedule:', scheduleId)
        return false
      }

      // Check if we have all required data
      if (!schedule.custom_id || !project.custom_id || !project.name || 
          !client.custom_id || !client.name) {
        console.log('❌ Missing required data for SharePoint folder creation:', {
          scheduleCustomId: schedule.custom_id,
          projectCustomId: project.custom_id,
          projectName: project.name,
          clientCustomId: client.custom_id,
          clientName: client.name
        })
        return false
      }

      // Format the date for folder naming - using START date (scheduled_date)
      // Convert to India timezone (IST) for consistent folder naming
      const scheduleDate = new Date(schedule.scheduled_date)
      const istDate = new Date(scheduleDate.toLocaleString("en-US", {timeZone: "Asia/Kolkata"}))
      const formattedDate = `${istDate.getFullYear()}-${String(istDate.getMonth() + 1).padStart(2, '0')}-${String(istDate.getDate()).padStart(2, '0')}`
      
      console.log('🚀 Creating SharePoint folder with parameters:', {
        scheduleId: schedule.id,
        scheduleCustomId: schedule.custom_id,
        scheduleDate: formattedDate,
        projectCustomId: project.custom_id,
        projectName: project.name,
        clientCustomId: client.custom_id,
        clientName: client.name
      })
      
      // Create the folder structure
      await createScheduleFolder(
        schedule.id,                    // scheduleId
        schedule.custom_id,             // scheduleCustomId
        formattedDate,                  // scheduleDate
        project.custom_id,              // projectCustomId
        project.name,                   // projectName
        client.custom_id,               // clientCustomId
        client.name                     // clientName
      )
      
      console.log('✅ SharePoint folder created successfully for schedule:', scheduleId)
      return true
    } catch (error) {
      console.error('❌ Failed to create SharePoint folder for schedule:', scheduleId, error)
      return false
    }
  }

  /**
   * Ensures SharePoint folders are created for all schedules that don't have them
   */
  static async ensureAllScheduleFolders(): Promise<{ success: number; failed: number; details: string[] }> {
    console.log('🚀 Starting ensureAllScheduleFolders...')
    
    try {
      // Get all schedules using direct Supabase query with service role
      console.log('📋 Fetching all schedules with server client...')
      const serverSupabase = createServerSupabaseClient()
      const { data: schedules, error } = await serverSupabase
        .from('schedules')
        .select(`
          id,
          custom_id,
          project_id,
          scheduled_date
        `)
        .order('scheduled_date')

      if (error) {
        console.error('❌ Error fetching schedules:', error)
        throw new Error(`Failed to fetch schedules: ${error.message}`)
      }

      console.log(`📊 Found ${schedules?.length || 0} schedules`)
      
      if (schedules && schedules.length > 0) {
        console.log('🔍 First few schedules:', schedules.slice(0, 3).map((s: any) => ({
          id: s.id,
          custom_id: s.custom_id,
          project_id: s.project_id,
          scheduled_date: s.scheduled_date
        })))
      }

      let successCount = 0
      let failedCount = 0
      const details: string[] = []

      for (const schedule of schedules || []) {
        try {
          console.log(`🔄 Processing schedule ${schedule.id}...`)
          await SharePointService.ensureScheduleFolder(schedule.id)
          successCount++
          details.push(`✅ Schedule ${schedule.id}: Folder ensured`)
          console.log(`✅ Successfully processed schedule ${schedule.id}`)
        } catch (error) {
          failedCount++
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          details.push(`❌ Schedule ${schedule.id}: ${errorMessage}`)
          console.error(`❌ Failed to process schedule ${schedule.id}:`, error)
        }

        // Small delay to prevent API overload
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      console.log(`🎯 Completed: ${successCount} successful, ${failedCount} failed`)
      return { success: successCount, failed: failedCount, details }
    } catch (error) {
      console.error('💥 Error in ensureAllScheduleFolders:', error)
      throw error
    }
  }
}