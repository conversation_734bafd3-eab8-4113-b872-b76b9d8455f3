import type { ExpenseCategory } from '@/types'

// Expense Categories
export const EXPENSE_CATEGORIES: { value: ExpenseCategory; label: string }[] = [
  { value: 'cymatics', label: 'Cymatics' },
  { value: 'salary', label: 'Salary' },
  { value: 'gadgets', label: 'Gadgets' },
  { value: 'outsourcing', label: 'Outsourcing' },
  { value: 'asset', label: 'Asset' },
  { value: 'loan_repayment', label: 'Loan Repayment' },
  { value: 'investments', label: 'Investments' },
  { value: 'fuel_travel', label: 'Fuel & Travel' },
  { value: 'food_snacks', label: 'Food & Snacks' },
  { value: 'others', label: 'Others' },
  { value: 'entertainment', label: 'Entertainment' },
  { value: 'gopi', label: 'Gopi' },
  { value: 'yaso', label: 'Yaso' },
  { value: 'adithyan', label: 'Adithyan' },
]

// Outsourcing Categories
export const OUTSOURCING_CATEGORIES = [
  'Photo',
  'Video',
  'Editor',
  'Drone',
  'Pilot',
  'Post-processing',
  'Softwares',
] as const

export type OutsourcingCategory = typeof OUTSOURCING_CATEGORIES[number]

// Client Types
export const CLIENT_TYPES = [
  'Wedding',
  'Corporate',
  'Movie',
  'Govt',
  'NGO',
  'Survey',
  'Surveillance',
  'Real estate',
  'News',
  'Event',
  'Collaboration',
] as const

export type ClientType = typeof CLIENT_TYPES[number]
