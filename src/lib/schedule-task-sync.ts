import { createClientSupabaseClient } from '@/lib/auth'
import { getDefaultTasksForClientType } from '@/lib/default-tasks'
import type { Task, Schedule } from '@/types'

/**
 * Synchronizes task due dates when a schedule's start time changes
 */
export async function syncTaskDueDatesWithSchedule(
  scheduleId: string, 
  newScheduledDate: string,
  oldScheduledDate?: string
): Promise<void> {
  const supabase = createClientSupabaseClient()

  try {
    console.log('🔄 Syncing task due dates for schedule:', {
      scheduleId,
      newScheduledDate,
      oldScheduledDate
    })

    // Get the schedule with project and client information
    const { data: schedule, error: scheduleError } = await supabase
      .from('schedules')
      .select(`
        id,
        project_id,
        scheduled_date,
        projects!inner (
          id,
          client_id,
          clients!inner (
            client_type
          )
        )
      `)
      .eq('id', scheduleId)
      .single()

    if (scheduleError || !schedule) {
      console.error('❌ Failed to fetch schedule for task sync:', scheduleError)
      return
    }

    // Get all tasks related to this schedule
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('*')
      .eq('shoot_id', scheduleId)

    if (tasksError) {
      console.error('❌ Failed to fetch tasks for schedule:', tasksError)
      return
    }

    if (!tasks || tasks.length === 0) {
      console.log('ℹ️ No tasks found for schedule:', scheduleId)
      return
    }

    console.log(`📋 Found ${tasks.length} tasks to sync for schedule:`, scheduleId)

    // Get the task templates for this client type to understand the due date logic
    const clientType = schedule.projects.clients.client_type
    const templates = getDefaultTasksForClientType(clientType)

    // Create a map of task titles to their templates for quick lookup
    const templateMap = new Map(templates.map(t => [t.title, t]))

    const tasksToUpdate: Array<{ id: string; due_date: string }> = []

    for (const task of tasks) {
      const template = templateMap.get(task.title)
      if (!template) {
        console.warn(`⚠️ No template found for task: ${task.title}`)
        continue
      }

      let newDueDate: string | null = null

      // Special case: Shoot task should have due date as the schedule start time
      if (task.title === 'Shoot') {
        newDueDate = newScheduledDate
      }
      // Calculate due date based on template rules
      else if (template.dueDaysAfterShoot) {
        const scheduleDateTime = new Date(newScheduledDate)
        const dueDateObj = new Date(scheduleDateTime)
        dueDateObj.setDate(dueDateObj.getDate() + template.dueDaysAfterShoot)
        newDueDate = dueDateObj.toISOString().split('T')[0] // Format as YYYY-MM-DD
      }

      // Only update if the due date actually changed
      if (newDueDate && newDueDate !== task.due_date) {
        tasksToUpdate.push({
          id: task.id,
          due_date: newDueDate
        })
        
        console.log(`📅 Task "${task.title}" due date: ${task.due_date} → ${newDueDate}`)
      }
    }

    if (tasksToUpdate.length === 0) {
      console.log('ℹ️ No task due dates need updating')
      return
    }

    // Update all tasks in batch
    console.log(`🔄 Updating ${tasksToUpdate.length} task due dates...`)
    
    for (const taskUpdate of tasksToUpdate) {
      const { error: updateError } = await supabase
        .from('tasks')
        .update({ 
          due_date: taskUpdate.due_date,
          updated_at: new Date().toISOString()
        })
        .eq('id', taskUpdate.id)

      if (updateError) {
        console.error(`❌ Failed to update task ${taskUpdate.id}:`, updateError)
      } else {
        console.log(`✅ Updated task ${taskUpdate.id} due date to ${taskUpdate.due_date}`)
      }
    }

    console.log('✅ Task due date sync completed for schedule:', scheduleId)

  } catch (error) {
    console.error('❌ Error syncing task due dates:', error)
    throw error
  }
}

/**
 * Checks if a schedule date change requires task due date updates
 */
export function shouldSyncTaskDueDates(
  oldScheduledDate: string,
  newScheduledDate: string
): boolean {
  // Compare dates (ignoring time for now, but could be enhanced)
  const oldDate = new Date(oldScheduledDate).toDateString()
  const newDate = new Date(newScheduledDate).toDateString()
  
  return oldDate !== newDate
}

/**
 * Syncs task due dates for project-level tasks when any schedule in the project changes
 */
export async function syncProjectTaskDueDates(
  projectId: string,
  earliestScheduleDate: string
): Promise<void> {
  const supabase = createClientSupabaseClient()

  try {
    console.log('🔄 Syncing project-level task due dates for project:', projectId)

    // Get project-level tasks (tasks without shoot_id)
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('*')
      .eq('project_id', projectId)
      .is('shoot_id', null)

    if (tasksError) {
      console.error('❌ Failed to fetch project tasks:', tasksError)
      return
    }

    if (!tasks || tasks.length === 0) {
      console.log('ℹ️ No project-level tasks found for project:', projectId)
      return
    }

    // Get project and client information
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        id,
        client_id,
        clients!inner (
          client_type
        )
      `)
      .eq('id', projectId)
      .single()

    if (projectError || !project) {
      console.error('❌ Failed to fetch project for task sync:', projectError)
      return
    }

    // Get task templates for this client type
    const clientType = project.clients.client_type
    const templates = getDefaultTasksForClientType(clientType)
    const projectTemplates = templates.filter(t => t.isProjectTask)

    // Create a map of task titles to their templates
    const templateMap = new Map(projectTemplates.map(t => [t.title, t]))

    const tasksToUpdate: Array<{ id: string; due_date: string }> = []

    for (const task of tasks) {
      const template = templateMap.get(task.title)
      if (!template || !template.dueDaysAfterShoot) {
        continue
      }

      // Calculate new due date based on earliest schedule date
      const scheduleDateTime = new Date(earliestScheduleDate)
      const dueDateObj = new Date(scheduleDateTime)
      dueDateObj.setDate(dueDateObj.getDate() + template.dueDaysAfterShoot)
      const newDueDate = dueDateObj.toISOString().split('T')[0]

      // Only update if the due date actually changed
      if (newDueDate !== task.due_date) {
        tasksToUpdate.push({
          id: task.id,
          due_date: newDueDate
        })
        
        console.log(`📅 Project task "${task.title}" due date: ${task.due_date} → ${newDueDate}`)
      }
    }

    // Update all tasks in batch
    for (const taskUpdate of tasksToUpdate) {
      const { error: updateError } = await supabase
        .from('tasks')
        .update({ 
          due_date: taskUpdate.due_date,
          updated_at: new Date().toISOString()
        })
        .eq('id', taskUpdate.id)

      if (updateError) {
        console.error(`❌ Failed to update project task ${taskUpdate.id}:`, updateError)
      } else {
        console.log(`✅ Updated project task ${taskUpdate.id} due date to ${taskUpdate.due_date}`)
      }
    }

    console.log('✅ Project task due date sync completed for project:', projectId)

  } catch (error) {
    console.error('❌ Error syncing project task due dates:', error)
    throw error
  }
}
