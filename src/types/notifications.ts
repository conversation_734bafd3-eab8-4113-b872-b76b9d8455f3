export interface Notification {
  id: string
  user_id: string
  title: string
  message: string
  type: NotificationType
  category: NotificationCategory
  priority: NotificationPriority
  read: boolean
  action_url?: string
  action_label?: string
  metadata?: Record<string, any>
  created_at: string
  read_at?: string
  expires_at?: string
}

export type NotificationType = 
  | 'info'
  | 'success' 
  | 'warning'
  | 'error'
  | 'task'
  | 'project'
  | 'schedule'
  | 'payment'
  | 'system'

export type NotificationCategory =
  | 'task_assigned'
  | 'task_due'
  | 'task_overdue'
  | 'task_completed'
  | 'project_created'
  | 'project_updated'
  | 'schedule_upcoming'
  | 'schedule_changed'
  | 'payment_received'
  | 'payment_overdue'
  | 'system_update'
  | 'user_mention'
  | 'deadline_reminder'
  | 'general'

export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent'

export interface NotificationPreferences {
  id: string
  user_id: string
  email_notifications: boolean
  push_notifications: boolean
  in_app_notifications: boolean
  categories: Record<NotificationCategory, {
    enabled: boolean
    email: boolean
    push: boolean
    in_app: boolean
  }>
  quiet_hours: {
    enabled: boolean
    start_time: string // HH:MM format
    end_time: string   // HH:MM format
  }
  created_at: string
  updated_at: string
}

export interface CreateNotificationRequest {
  user_id: string
  title: string
  message: string
  type: NotificationType
  category: NotificationCategory
  priority?: NotificationPriority
  action_url?: string
  action_label?: string
  metadata?: Record<string, any>
  expires_at?: string
}

export interface NotificationFilters {
  read?: boolean
  type?: NotificationType
  category?: NotificationCategory
  priority?: NotificationPriority
  limit?: number
  offset?: number
}

export interface NotificationStats {
  total: number
  unread: number
  by_type: Record<NotificationType, number>
  by_category: Record<NotificationCategory, number>
  by_priority: Record<NotificationPriority, number>
}
