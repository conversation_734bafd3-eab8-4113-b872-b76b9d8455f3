---
description: Repository Information Overview
alwaysApply: true
---

# Cymatics Project Information

## Summary
Cymatics is a Next.js web application that provides a project management system with structured IDs for clients, projects, schedules, and vendors. It integrates with Supabase for backend services and database management, and includes features for SharePoint integration, task management, and location services.

## Structure
- **src/**: Main application code with Next.js app router structure
  - **app/**: Next.js app router pages and API routes
  - **components/**: React components organized by functionality
  - **lib/**: Utility functions and services
  - **hooks/**: Custom React hooks for data fetching and state management
  - **contexts/**: React context providers
- **supabase/**: Database migrations and Supabase configuration
- **public/**: Static assets and files
- **testsprite_tests/**: Test files for backend functionality

## Language & Runtime
**Language**: TypeScript
**Version**: ES2017 target with Next.js 15.4.2
**Build System**: Next.js build system with Turbopack
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- Next.js 15.4.2 (React 19.1.0)
- Supabase Auth and Client (v2.52.0)
- Radix UI components for UI elements
- TailwindCSS for styling
- React Hook Form (v7.60.0) with Zod (v4.0.5) for form validation
- Google Maps integration (@googlemaps/js-api-loader)
- Date handling with date-fns (v4.1.0)

**Development Dependencies**:
- TypeScript (v5)
- ESLint (v9)
- TailwindCSS tooling

## Build & Installation
```bash
# Install dependencies
npm install

# Development server with Turbopack
npm run dev

# Production build
npm run build

# Start production server
npm start
```

## Database
**Provider**: Supabase
**Schema**: SQL migrations in supabase/migrations/
**Key Features**:
- Structured ID system for entities (CYMCL, CYMPR, CYM, CYMOU prefixes)
- Row-level security policies
- Custom functions for ID generation and management

## Testing
**Framework**: Jest (implied from test file structure)
**Test Location**: src/lib/__tests__/
**Test Files**: Limited tests found, primarily for utility functions
**Run Command**: No explicit test command in package.json

## Frontend Architecture
**Framework**: Next.js with App Router
**State Management**: React Context and custom hooks
**UI Components**: Mix of Radix UI primitives and custom components
**Styling**: TailwindCSS with custom configuration
**Real-time Features**: Supabase real-time subscriptions (useRealTime hooks)

## Key Features
- Structured ID system for entities (clients, projects, schedules, vendors)
- SharePoint integration for document management
- Task management and synchronization
- Location services with Google Maps integration
- Background job processing
- Real-time updates via Supabase subscriptions