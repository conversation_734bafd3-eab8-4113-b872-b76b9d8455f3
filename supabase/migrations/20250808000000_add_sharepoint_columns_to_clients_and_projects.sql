-- Migration: Add SharePoint folder columns to clients and projects tables
-- Date: 2025-08-08
-- Description: Adds columns to store SharePoint folder IDs, URLs, and share links for clients and projects

-- Add SharePoint columns to clients table
ALTER TABLE clients 
ADD COLUMN IF NOT EXISTS sharepoint_folder_id TEXT,
ADD COLUMN IF NOT EXISTS sharepoint_folder_url TEXT,
ADD COLUMN IF NOT EXISTS sharepoint_share_link TEXT;

-- Add SharePoint columns to projects table
ALTER TABLE projects 
ADD COLUMN IF NOT EXISTS sharepoint_folder_id TEXT,
ADD COLUMN IF NOT EXISTS sharepoint_folder_url TEXT,
ADD COLUMN IF NOT EXISTS sharepoint_share_link TEXT;

-- Add SharePoint columns to schedules table (if not already present)
ALTER TABLE schedules 
ADD COLUMN IF NOT EXISTS sharepoint_folder_id TEXT,
ADD COLUMN IF NOT EXISTS sharepoint_folder_url TEXT,
ADD COLUMN IF NOT EXISTS sharepoint_share_link TEXT;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_clients_sharepoint_folder_id ON clients(sharepoint_folder_id);
CREATE INDEX IF NOT EXISTS idx_projects_sharepoint_folder_id ON projects(sharepoint_folder_id);
CREATE INDEX IF NOT EXISTS idx_schedules_sharepoint_folder_id ON schedules(sharepoint_folder_id);

-- Add comments to document the new columns
COMMENT ON COLUMN clients.sharepoint_folder_id IS 'SharePoint folder ID for client folder';
COMMENT ON COLUMN clients.sharepoint_folder_url IS 'SharePoint folder URL for client folder';
COMMENT ON COLUMN clients.sharepoint_share_link IS 'Public share link for client folder';

COMMENT ON COLUMN projects.sharepoint_folder_id IS 'SharePoint folder ID for project folder';
COMMENT ON COLUMN projects.sharepoint_folder_url IS 'SharePoint folder URL for project folder';
COMMENT ON COLUMN projects.sharepoint_share_link IS 'Public share link for project folder';

COMMENT ON COLUMN schedules.sharepoint_folder_id IS 'SharePoint folder ID for schedule folder';
COMMENT ON COLUMN schedules.sharepoint_folder_url IS 'SharePoint folder URL for schedule folder';
COMMENT ON COLUMN schedules.sharepoint_share_link IS 'Public share link for schedule folder';