-- Up: Create triggers and constraints for outsourcing_vendors (CYMOU series)

-- Defensive bootstrap to ensure trigger function exists (matches existing pattern)
DO $bootstrap$
BEGIN
  EXECUTE $sql$
    create schema if not exists cymatics_ids;
    create table if not exists cymatics_ids.counters (
      entity_type text not null,
      year        int  not null,
      last_number int  not null default 0,
      updated_at  timestamptz not null default now(),
      primary key (entity_type, year)
    );
    create or replace function cymatics_ids.apply_structured_id()
    returns trigger
    language plpgsql
    security definer
    set search_path = public, pg_temp
    as $fn$
    declare
      v_prefix text;
      v_entity_type text;
      v_now timestamptz;
      v_year int;
      v_next int;
      v_id text;
    begin
      if new.custom_id is not null then
        return new;
      end if;

      v_prefix := tg_argv[0];
      v_entity_type := tg_argv[1];

      v_now := (now() at time zone 'Asia/Kolkata');
      v_year := extract(year from v_now);

      insert into cymatics_ids.counters(entity_type, year, last_number, updated_at)
      values (v_entity_type, v_year, 1, now())
      on conflict (entity_type, year)
      do update set last_number = cymatics_ids.counters.last_number + 1,
                    updated_at = now()
      returning last_number
      into v_next;

      if v_next > 999 then
        raise exception 'Structured ID overflow for % in year % (next=%). Max is 999', v_entity_type, v_year, v_next
          using errcode = 'P0001';
      end if;

      v_id := format('%s-%s-%s',
                     v_prefix,
                     to_char(v_now, 'YY'),
                     lpad(v_next::text, 3, '0'));

      new.custom_id := v_id;
      return new;
    end
    $fn$;
  $sql$;
END
$bootstrap$;

-- Create unique index (nullable during transition)
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'public' AND c.relname = 'outsourcing_vendors' AND c.relkind = 'r'
  ) THEN
    CREATE UNIQUE INDEX IF NOT EXISTS idx_outsourcing_vendors_custom_id_unique
      ON public.outsourcing_vendors(custom_id) WHERE custom_id IS NOT NULL;
  ELSE
    RAISE NOTICE 'public.outsourcing_vendors not found; skipping index creation';
  END IF;
END$$;

-- Attach BEFORE INSERT trigger to assign CYMOU IDs
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'public' AND c.relname = 'outsourcing_vendors' AND c.relkind = 'r'
  ) THEN
    DROP TRIGGER IF EXISTS outsourcing_vendors_custom_id_bi ON public.outsourcing_vendors;
    CREATE TRIGGER outsourcing_vendors_custom_id_bi
    BEFORE INSERT ON public.outsourcing_vendors
    FOR EACH ROW EXECUTE FUNCTION cymatics_ids.apply_structured_id('CYMOU','vendor');
  ELSE
    RAISE NOTICE 'public.outsourcing_vendors not found; skipping trigger creation';
  END IF;
END$$;

-- After backfill, enforce NOT NULL on custom_id
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'public' AND c.relname = 'outsourcing_vendors' AND c.relkind = 'r'
  ) THEN
    ALTER TABLE public.outsourcing_vendors ALTER COLUMN custom_id SET NOT NULL;
  END IF;
END$$;

-- Optional strict format check (commented until validated in staging)
-- ALTER TABLE public.outsourcing_vendors
--   ADD CONSTRAINT outsourcing_vendors_custom_id_format_chk
--   CHECK (custom_id ~ '^CYMOU-[0-9]{2}-[0-9]{3}$');

-- Down: relax NOT NULL, drop trigger and index
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'public' AND c.relname = 'outsourcing_vendors' AND c.relkind = 'r'
  ) THEN
    ALTER TABLE public.outsourcing_vendors ALTER COLUMN custom_id DROP NOT NULL;
    DROP TRIGGER IF EXISTS outsourcing_vendors_custom_id_bi ON public.outsourcing_vendors;
    DROP INDEX IF EXISTS idx_outsourcing_vendors_custom_id_unique;
  END IF;
END$$;