-- Ensure schema and counters table exist defensively (idempotent)
create schema if not exists cymatics_ids;

create table if not exists cymatics_ids.counters (
  entity_type text not null,
  year        int  not null,
  last_number int  not null default 0,
  updated_at  timestamptz not null default now(),
  primary key (entity_type, year)
);

-- Recreate trigger function with correct zero-arg signature so EXECUTE FUNCTION ...('arg1','arg2') works
-- This function reads TG_ARGV[0] as prefix and TG_ARGV[1] as entity_type
create or replace function cymatics_ids.apply_structured_id()
returns trigger
language plpgsql
security definer
set search_path = public, pg_temp
as $$
declare
  v_prefix text;
  v_entity_type text;
  v_now timestamptz;
  v_year int;
  v_next int;
  v_id text;
begin
  -- Only assign when target column is NULL (on INSERT or on UPDATE OF custom_id to NULL)
  if new.custom_id is not null then
    return new;
  end if;

  v_prefix := tg_argv[0];
  v_entity_type := tg_argv[1];

  -- Compute current year in Asia/Kolkata
  v_now := (now() at time zone 'Asia/Kolkata');
  v_year := extract(year from v_now);

  -- Upsert counter row and bump last_number atomically
  insert into cymatics_ids.counters(entity_type, year, last_number, updated_at)
  values (v_entity_type, v_year, 1, now())
  on conflict (entity_type, year)
  do update set last_number = cymatics_ids.counters.last_number + 1,
                updated_at = now()
  returning last_number
  into v_next;

  if v_next > 999 then
    raise exception 'Structured ID overflow for % in year % (next=%). Max is 999', v_entity_type, v_year, v_next
      using errcode = 'P0001';
  end if;

  v_id := format('%s-%s-%s',
                 v_prefix,
                 to_char(v_now, 'YY'),
                 lpad(v_next::text, 3, '0'));

  new.custom_id := v_id;
  return new;
end
$$;

-- No down section; replacement is idempotent and safe