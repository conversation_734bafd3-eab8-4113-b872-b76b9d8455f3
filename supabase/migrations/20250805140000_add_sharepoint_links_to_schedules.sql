-- Migration: Add SharePoint folder links to schedules table
-- Date: 2025-08-05
-- Description: Adds columns to store SharePoint folder IDs and URLs for Raw and Output folders

-- Add columns for SharePoint folder information
ALTER TABLE schedules 
ADD COLUMN IF NOT EXISTS sharepoint_raw_folder_id TEXT,
ADD COLUMN IF NOT EXISTS sharepoint_raw_folder_url TEXT,
ADD COLUMN IF NOT EXISTS sharepoint_output_folder_id TEXT,
ADD COLUMN IF NOT EXISTS sharepoint_output_folder_url TEXT;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_schedules_sharepoint_raw_folder_id ON schedules(sharepoint_raw_folder_id);
CREATE INDEX IF NOT EXISTS idx_schedules_sharepoint_output_folder_id ON schedules(sharepoint_output_folder_id);

-- Add comments to document the new columns
COMMENT ON COLUMN schedules.sharepoint_raw_folder_id IS 'SharePoint folder ID for raw files';
COMMENT ON COLUMN schedules.sharepoint_raw_folder_url IS 'SharePoint folder URL for raw files';
COMMENT ON COLUMN schedules.sharepoint_output_folder_id IS 'SharePoint folder ID for output files';
COMMENT ON COLUMN schedules.sharepoint_output_folder_url IS 'SharePoint folder URL for output files';