-- Up: Extend public preview RPC to support vendors (CYMOU)

-- Replace or create a preview RPC that maps 'vendor' to CYMOU, mirroring existing mappings.
create or replace function public.peek_next_entity_id(entity_type text)
returns text
language plpgsql
security definer
stable
set search_path = public, cymatics_ids
as $$
declare
  mapped_type text;
  mapped_prefix text;
  result text;
begin
  -- Map allowed entity types to internal type and fixed prefix
  if entity_type = 'client' then
    mapped_type := 'client';
    mapped_prefix := 'CYMCL';
  elsif entity_type = 'project' then
    mapped_type := 'project';
    mapped_prefix := 'CYMPR';
  elsif entity_type = 'schedule' then
    mapped_type := 'schedule';
    mapped_prefix := 'CYM';
  elsif entity_type = 'vendor' then
    mapped_type := 'vendor';
    mapped_prefix := 'CYMOU';
  else
    raise exception 'invalid entity_type';
  end if;

  -- Delegate to generator in cymatics_ids schema (peek, non-reserving)
  select cymatics_ids.peek_next_structured_id(mapped_type, mapped_prefix) into result;

  return result;
end;
$$;

-- Ensure ownership under admin role (adjust if your admin role differs)
do $$
begin
  begin
    alter function public.peek_next_entity_id(text) owner to postgres;
  exception
    when insufficient_privilege or invalid_authorization_specification then
      begin
        alter function public.peek_next_entity_id(text) owner to supabase_admin;
      exception
        when others then
          null;
      end;
  end;
end$$;

-- Grant EXECUTE to authenticated (and service_role if present). DO NOT grant to anon.
grant execute on function public.peek_next_entity_id(text) to authenticated;
do $$
begin
  perform 1 from pg_roles where rolname = 'service_role';
  if found then
    grant execute on function public.peek_next_entity_id(text) to service_role;
  end if;
end$$;

-- Down: No-op (function remains compatible for all entity types)