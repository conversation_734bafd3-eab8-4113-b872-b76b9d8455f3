-- Up: safety hotfix to ensure cymatics_ids schema and counters exist (idempotent)
CREATE SCHEMA IF NOT EXISTS cymatics_ids;
CREATE TABLE IF NOT EXISTS cymatics_ids.counters (
  entity_type text NOT NULL,
  year smallint NOT NULL,
  last_number integer NOT NULL DEFAULT 0 CHECK (last_number >= 0 AND last_number <= 999),
  updated_at timestamptz NOT NULL DEFAULT now(),
  PRIMARY KEY(entity_type, year)
);

-- Down: no-op (keep counters; core migration handles teardown)
