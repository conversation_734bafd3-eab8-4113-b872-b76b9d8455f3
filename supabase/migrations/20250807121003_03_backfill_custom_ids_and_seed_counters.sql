-- Up: backfill IDs deterministically and seed counters
-- Ensure columns exist even if previous migration was skipped
ALTER TABLE public.clients   ADD COLUMN IF NOT EXISTS custom_id text;
ALTER TABLE public.projects  ADD COLUMN IF NOT EXISTS custom_id text;
ALTER TABLE public.schedules ADD COLUMN IF NOT EXISTS custom_id text;

-- Helper CTEs are now used per UPDATE to avoid CTE scope issues
-- Clients backfill
WITH c AS (
  SELECT id,
         to_char((COALESCE(created_at, now()) AT TIME ZONE 'Asia/Kolkata'), 'YY') AS yy,
         EXTRACT(YEAR FROM (COALESCE(created_at, now()) AT TIME ZONE 'Asia/Kolkata'))::int AS yr,
         row_number() OVER (
           PARTITION BY EXTRACT(YEAR FROM (COALESCE(created_at, now()) AT TIME ZONE 'Asia/Kolkata'))
           ORDER BY created_at NULLS LAST, id
         ) AS rn
  FROM public.clients
  WHERE custom_id IS NULL
)
UPDATE public.clients t
SET custom_id = 'CYMCL-' || c.yy || '-' || lpad(c.rn::text, 3, '0')
FROM c WHERE t.id = c.id;

-- Projects backfill
WITH p AS (
  SELECT id,
         to_char((COALESCE(created_at, now()) AT TIME ZONE 'Asia/Kolkata'), 'YY') AS yy,
         EXTRACT(YEAR FROM (COALESCE(created_at, now()) AT TIME ZONE 'Asia/Kolkata'))::int AS yr,
         row_number() OVER (
           PARTITION BY EXTRACT(YEAR FROM (COALESCE(created_at, now()) AT TIME ZONE 'Asia/Kolkata'))
           ORDER BY created_at NULLS LAST, id
         ) AS rn
  FROM public.projects
  WHERE custom_id IS NULL
)
UPDATE public.projects t
SET custom_id = 'CYMPR-' || p.yy || '-' || lpad(p.rn::text, 3, '0')
FROM p WHERE t.id = p.id;

-- Schedules backfill
WITH s AS (
  SELECT id,
         to_char((COALESCE(created_at, now()) AT TIME ZONE 'Asia/Kolkata'), 'YY') AS yy,
         EXTRACT(YEAR FROM (COALESCE(created_at, now()) AT TIME ZONE 'Asia/Kolkata'))::int AS yr,
         row_number() OVER (
           PARTITION BY EXTRACT(YEAR FROM (COALESCE(created_at, now()) AT TIME ZONE 'Asia/Kolkata'))
           ORDER BY created_at NULLS LAST, id
         ) AS rn
  FROM public.schedules
  WHERE custom_id IS NULL
)
UPDATE public.schedules t
SET custom_id = 'CYM-' || s.yy || '-' || lpad(s.rn::text, 3, '0')
FROM s WHERE t.id = s.id;

-- Seed counters to the max used per year
INSERT INTO cymatics_ids.counters(entity_type, year, last_number)
SELECT 'client', yr::smallint, max(COALESCE(substring(custom_id from '([0-9]{3})$')::int,0))
FROM (
  SELECT EXTRACT(YEAR FROM (created_at AT TIME ZONE 'Asia/Kolkata'))::int yr, custom_id FROM public.clients WHERE custom_id IS NOT NULL
) q
GROUP BY yr
ON CONFLICT (entity_type, year) DO UPDATE SET last_number = GREATEST(cymatics_ids.counters.last_number, EXCLUDED.last_number);

INSERT INTO cymatics_ids.counters(entity_type, year, last_number)
SELECT 'project', yr::smallint, max(COALESCE(substring(custom_id from '([0-9]{3})$')::int,0))
FROM (
  SELECT EXTRACT(YEAR FROM (created_at AT TIME ZONE 'Asia/Kolkata'))::int yr, custom_id FROM public.projects WHERE custom_id IS NOT NULL
) q
GROUP BY yr
ON CONFLICT (entity_type, year) DO UPDATE SET last_number = GREATEST(cymatics_ids.counters.last_number, EXCLUDED.last_number);

INSERT INTO cymatics_ids.counters(entity_type, year, last_number)
SELECT 'schedule', yr::smallint, max(COALESCE(substring(custom_id from '([0-9]{3})$')::int,0))
FROM (
  SELECT EXTRACT(YEAR FROM (created_at AT TIME ZONE 'Asia/Kolkata'))::int yr, custom_id FROM public.schedules WHERE custom_id IS NOT NULL
) q
GROUP BY yr
ON CONFLICT (entity_type, year) DO UPDATE SET last_number = GREATEST(cymatics_ids.counters.last_number, EXCLUDED.last_number);

-- Down: remove backfilled ids and counters entries (non-destructive)
UPDATE public.clients   SET custom_id = NULL;
UPDATE public.projects  SET custom_id = NULL;
UPDATE public.schedules SET custom_id = NULL;
DELETE FROM cymatics_ids.counters WHERE entity_type IN ('client','project','schedule');
