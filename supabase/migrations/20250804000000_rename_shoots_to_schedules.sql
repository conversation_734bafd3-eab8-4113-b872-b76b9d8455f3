-- Migration: Ren<PERSON> shoots table to schedules
-- Date: 2025-08-04
-- Description: Renames the shoots table to schedules to avoid naming conflicts

-- Rename the table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'shoots') THEN
    ALTER TABLE shoots RENAME TO schedules;
    
    -- Add comment to document the change
    COMMENT ON TABLE schedules IS 'Renamed from shoots table. Contains scheduled drone sessions/shoots for projects';

    -- Update the updated_at timestamp for any existing records
    UPDATE schedules SET updated_at = NOW() WHERE updated_at IS NOT NULL;
  END IF;
END
$$;