-- Up: constraints + triggers

-- Bootstrap: ensure function exists before referencing it (handles remote drift)
DO $bootstrap$
BEGIN
  EXECUTE $sql$
    create schema if not exists cymatics_ids;
    create table if not exists cymatics_ids.counters (
      entity_type text not null,
      year        int  not null,
      last_number int  not null default 0,
      updated_at  timestamptz not null default now(),
      primary key (entity_type, year)
    );
    create or replace function cymatics_ids.apply_structured_id()
    returns trigger
    language plpgsql
    security definer
    set search_path = public, pg_temp
    as $fn$
    declare
      v_prefix text;
      v_entity_type text;
      v_now timestamptz;
      v_year int;
      v_next int;
      v_id text;
    begin
      if new.custom_id is not null then
        return new;
      end if;

      v_prefix := tg_argv[0];
      v_entity_type := tg_argv[1];

      v_now := (now() at time zone 'Asia/Kolkata');
      v_year := extract(year from v_now);

      insert into cymatics_ids.counters(entity_type, year, last_number, updated_at)
      values (v_entity_type, v_year, 1, now())
      on conflict (entity_type, year)
      do update set last_number = cymatics_ids.counters.last_number + 1,
                    updated_at = now()
      returning last_number
      into v_next;

      if v_next > 999 then
        raise exception 'Structured ID overflow for % in year % (next=%). Max is 999', v_entity_type, v_year, v_next
          using errcode = 'P0001';
      end if;

      v_id := format('%s-%s-%s',
                     v_prefix,
                     to_char(v_now, 'YY'),
                     lpad(v_next::text, 3, '0'));

      new.custom_id := v_id;
      return new;
    end
    $fn$;
  $sql$;
END
$bootstrap$;

-- Unique indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_clients_custom_id_unique   ON public.clients(custom_id)   WHERE custom_id IS NOT NULL;
CREATE UNIQUE INDEX IF NOT EXISTS idx_projects_custom_id_unique  ON public.projects(custom_id)  WHERE custom_id IS NOT NULL;
CREATE UNIQUE INDEX IF NOT EXISTS idx_schedules_custom_id_unique ON public.schedules(custom_id) WHERE custom_id IS NOT NULL;

-- Guard against residual NULLs using UPDATE triggers before enforcing NOT NULL
DO $$
BEGIN
  DROP TRIGGER IF EXISTS clients_custom_id_bi ON public.clients;
  CREATE TRIGGER clients_custom_id_bi
  BEFORE INSERT OR UPDATE OF custom_id ON public.clients
  FOR EACH ROW EXECUTE FUNCTION cymatics_ids.apply_structured_id('CYMCL','client');

  DROP TRIGGER IF EXISTS projects_custom_id_bi ON public.projects;
  CREATE TRIGGER projects_custom_id_bi
  BEFORE INSERT OR UPDATE OF custom_id ON public.projects
  FOR EACH ROW EXECUTE FUNCTION cymatics_ids.apply_structured_id('CYMPR','project');

  DROP TRIGGER IF EXISTS schedules_custom_id_bi ON public.schedules;
  CREATE TRIGGER schedules_custom_id_bi
  BEFORE INSERT OR UPDATE OF custom_id ON public.schedules
  FOR EACH ROW EXECUTE FUNCTION cymatics_ids.apply_structured_id('CYM','schedule');
END $$;

-- Force UPDATE to fire triggers on existing NULLs
UPDATE public.clients   SET custom_id = NULL WHERE custom_id IS NULL;
UPDATE public.projects  SET custom_id = NULL WHERE custom_id IS NULL;
UPDATE public.schedules SET custom_id = NULL WHERE custom_id IS NULL;

-- Enforce NOT NULL
ALTER TABLE public.clients   ALTER COLUMN custom_id SET NOT NULL;
ALTER TABLE public.projects  ALTER COLUMN custom_id SET NOT NULL;
ALTER TABLE public.schedules ALTER COLUMN custom_id SET NOT NULL;

-- Recreate insert-only triggers for steady state
DROP TRIGGER IF EXISTS clients_custom_id_bi ON public.clients;
CREATE TRIGGER clients_custom_id_bi
BEFORE INSERT ON public.clients
FOR EACH ROW EXECUTE FUNCTION cymatics_ids.apply_structured_id('CYMCL','client');

DROP TRIGGER IF EXISTS projects_custom_id_bi ON public.projects;
CREATE TRIGGER projects_custom_id_bi
BEFORE INSERT ON public.projects
FOR EACH ROW EXECUTE FUNCTION cymatics_ids.apply_structured_id('CYMPR','project');

DROP TRIGGER IF EXISTS schedules_custom_id_bi ON public.schedules;
CREATE TRIGGER schedules_custom_id_bi
BEFORE INSERT ON public.schedules
FOR EACH ROW EXECUTE FUNCTION cymatics_ids.apply_structured_id('CYM','schedule');

-- Down: drop triggers, constraints, and relax not-null
DROP TRIGGER IF EXISTS schedules_custom_id_bi ON public.schedules;
DROP TRIGGER IF EXISTS projects_custom_id_bi ON public.projects;
DROP TRIGGER IF EXISTS clients_custom_id_bi ON public.clients;

DROP INDEX IF EXISTS idx_schedules_custom_id_unique;
DROP INDEX IF EXISTS idx_projects_custom_id_unique;
DROP INDEX IF EXISTS idx_clients_custom_id_unique;

ALTER TABLE public.schedules ALTER COLUMN custom_id DROP NOT NULL;
ALTER TABLE public.projects  ALTER COLUMN custom_id DROP NOT NULL;
ALTER TABLE public.clients   ALTER COLUMN custom_id DROP NOT NULL;
