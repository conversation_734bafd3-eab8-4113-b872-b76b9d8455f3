-- Migration: Add geo_metadata_link column to schedules table
-- Date: 2025-01-15
-- Description: Adds a column to store the SharePoint link to the geo_metadata.json file generated by heatmap automation

-- Add geo_metadata_link column to schedules table
ALTER TABLE schedules 
ADD COLUMN IF NOT EXISTS geo_metadata_link TEXT;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_schedules_geo_metadata_link ON schedules(geo_metadata_link);

-- Add comment to document the new column
COMMENT ON COLUMN schedules.geo_metadata_link IS 'Public SharePoint link to the geo_metadata.json file containing GPS coordinates and metadata from uploaded files';