-- Migration: Update schedule_vendors RLS policies to use get_user_role()
-- Date: 2025-08-05
-- Description: Updates the RLS policies on schedule_vendors to be more flexible for admins and managers

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view schedule vendors" ON schedule_vendors;
DROP POLICY IF EXISTS "Users can insert schedule vendors" ON schedule_vendors;
DROP POLICY IF EXISTS "Users can update schedule vendors" ON schedule_vendors;
DROP POLICY IF EXISTS "Users can delete schedule vendors" ON schedule_vendors;

-- More permissive policies for admin and manager roles
CREATE POLICY "Admins and managers can manage all schedule vendors" ON schedule_vendors
  FOR ALL USING (
    (get_user_role() IN ('admin', 'manager'))
  );

-- Stricter policies for other roles (e.g., pilot, editor)
CREATE POLICY "Users can view their assigned schedule vendors" ON schedule_vendors
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM schedules s
      WHERE s.id = schedule_vendors.schedule_id
        AND (
          s.pilot_id = auth.uid()
          OR get_user_role() IN ('admin', 'manager')
        )
    )
  );

CREATE POLICY "Users can insert vendors for their assigned schedules" ON schedule_vendors
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM schedules s
      WHERE s.id = schedule_vendors.schedule_id
        AND (
          s.pilot_id = auth.uid()
          OR get_user_role() IN ('admin', 'manager')
        )
    )
  );

CREATE POLICY "Users can update vendors for their assigned schedules" ON schedule_vendors
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM schedules s
      WHERE s.id = schedule_vendors.schedule_id
        AND (
          s.pilot_id = auth.uid()
          OR get_user_role() IN ('admin', 'manager')
        )
    )
  );

CREATE POLICY "Users can delete vendors for their assigned schedules" ON schedule_vendors
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM schedules s
      WHERE s.id = schedule_vendors.schedule_id
        AND (
          s.pilot_id = auth.uid()
          OR get_user_role() IN ('admin', 'manager')
        )
    )
  );