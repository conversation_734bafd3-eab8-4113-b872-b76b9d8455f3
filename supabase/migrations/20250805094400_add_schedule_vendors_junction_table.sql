-- Migration: Add schedule_vendors junction table for many-to-many relationship
-- Date: 2025-08-04
-- Description: Creates a junction table to support multiple vendors per schedule

-- Create the junction table
CREATE TABLE IF NOT EXISTS schedule_vendors (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  schedule_id UUID NOT NULL REFERENCES schedules(id) ON DELETE CASCADE,
  vendor_id UUID NOT NULL REFERENCES outsourcing_vendors(id) ON DELETE CASCADE,
  cost DECIMAL(10,2) DEFAULT 0,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique combination of schedule and vendor
  UNIQUE(schedule_id, vendor_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_schedule_vendors_schedule_id ON schedule_vendors(schedule_id);
CREATE INDEX IF NOT EXISTS idx_schedule_vendors_vendor_id ON schedule_vendors(vendor_id);

-- Add RLS (Row Level Security) policies
ALTER TABLE schedule_vendors ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view schedule vendors for schedules they have access to
CREATE POLICY "Users can view schedule vendors" ON schedule_vendors
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM schedules s
      WHERE s.id = schedule_vendors.schedule_id
    )
  );

-- Policy: Users can insert schedule vendors for schedules they can modify
CREATE POLICY "Users can insert schedule vendors" ON schedule_vendors
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM schedules s
      WHERE s.id = schedule_vendors.schedule_id
    )
  );

-- Policy: Users can update schedule vendors for schedules they can modify
CREATE POLICY "Users can update schedule vendors" ON schedule_vendors
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM schedules s
      WHERE s.id = schedule_vendors.schedule_id
    )
  );

-- Policy: Users can delete schedule vendors for schedules they can modify
CREATE POLICY "Users can delete schedule vendors" ON schedule_vendors
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM schedules s
      WHERE s.id = schedule_vendors.schedule_id
    )
  );

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Check if trigger exists before creating it
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'update_schedule_vendors_updated_at'
  ) THEN
    CREATE TRIGGER update_schedule_vendors_updated_at
      BEFORE UPDATE ON schedule_vendors
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  END IF;
END
$$;

-- Note: Keep the existing vendor_id and outsourcing_cost columns in schedules table
-- for backward compatibility and single-vendor scenarios
-- The new junction table will be used for multi-vendor scenarios
