BEGIN;

-- 1) Drop legacy vendor columns from schedules
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'schedules' AND column_name = 'vendor_id'
  ) THEN
    ALTER TABLE public.schedules DROP COLUMN vendor_id;
  END IF;

  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'schedules' AND column_name = 'outsourcing_cost'
  ) THEN
    ALTER TABLE public.schedules DROP COLUMN outsourcing_cost;
  END IF;
END$$;

-- keep is_outsourced as requested

-- 2) Ensure junction table exists (no-op if already created by earlier migration)
CREATE TABLE IF NOT EXISTS public.schedule_vendors (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  schedule_id uuid NOT NULL REFERENCES public.schedules(id) ON DELETE CASCADE,
  vendor_id uuid NOT NULL REFERENCES public.outsourcing_vendors(id) ON DELETE RESTRICT,
  cost numeric NULL,
  notes text NULL,
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Helpful index for lookups
CREATE INDEX IF NOT EXISTS idx_schedule_vendors_schedule_id ON public.schedule_vendors(schedule_id);
CREATE INDEX IF NOT EXISTS idx_schedule_vendors_vendor_id ON public.schedule_vendors(vendor_id);

-- 3) Replace create_schedule_with_vendors to ONLY use schedule_vendors
CREATE OR REPLACE FUNCTION public.create_schedule_with_vendors(
  p_project_id UUID,
  p_scheduled_date TIMESTAMPTZ,
  p_scheduled_end_date TIMESTAMPTZ,
  p_pilot_id UUID,
  p_amount DECIMAL,
  p_location TEXT,
  p_google_maps_link TEXT,
  p_notes TEXT,
  p_is_recurring BOOLEAN,
  p_recurring_pattern TEXT,
  p_is_outsourced BOOLEAN,
  p_vendors JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
AS $function$
DECLARE
  new_schedule_id UUID;
  new_schedule JSONB;
  vendor_record JSONB;
BEGIN
  INSERT INTO public.schedules (
    project_id, scheduled_date, scheduled_end_date, pilot_id, amount,
    location, google_maps_link, notes, is_recurring, recurring_pattern, is_outsourced
  ) VALUES (
    p_project_id, p_scheduled_date, p_scheduled_end_date, p_pilot_id, p_amount,
    p_location, p_google_maps_link, p_notes, p_is_recurring, p_recurring_pattern::recurring_pattern, p_is_outsourced
  ) RETURNING id INTO new_schedule_id;

  -- Insert vendors into junction table (driven solely by p_vendors)
  IF p_vendors IS NOT NULL AND jsonb_typeof(p_vendors) = 'array' AND jsonb_array_length(p_vendors) > 0 THEN
    FOR vendor_record IN SELECT * FROM jsonb_array_elements(p_vendors)
    LOOP
      INSERT INTO public.schedule_vendors (schedule_id, vendor_id, cost, notes)
      VALUES (
        new_schedule_id,
        (vendor_record->>'vendor_id')::UUID,
        NULLIF(vendor_record->>'cost','')::DECIMAL,
        vendor_record->>'notes'
      );
    END LOOP;
  END IF;

  SELECT jsonb_build_object(
    'id', s.id,
    'project_id', s.project_id,
    'scheduled_date', s.scheduled_date,
    'scheduled_end_date', s.scheduled_end_date,
    'pilot_id', s.pilot_id,
    'amount', s.amount,
    'location', s.location,
    'google_maps_link', s.google_maps_link,
    'notes', s.notes,
    'is_recurring', s.is_recurring,
    'recurring_pattern', s.recurring_pattern,
    'is_outsourced', s.is_outsourced
  )
  INTO new_schedule
  FROM public.schedules s
  WHERE s.id = new_schedule_id;

  RETURN new_schedule;
END;
$function$;

-- 4) Replace update_schedule_with_vendors to ONLY use schedule_vendors
-- Use the consolidated signature that exists in production:
-- public.update_schedule_with_vendors(p_schedule_id uuid, p_updates jsonb, p_vendors jsonb)
CREATE OR REPLACE FUNCTION public.update_schedule_with_vendors(
  p_schedule_id uuid,
  p_updates jsonb,
  p_vendors jsonb
)
RETURNS jsonb
LANGUAGE plpgsql
AS $function$
DECLARE
  updated_schedule jsonb;
  vendor_record jsonb;
BEGIN
  -- Update fields from p_updates, preserving existing values if omitted
  UPDATE public.schedules
  SET
    scheduled_date       = COALESCE((p_updates->>'scheduled_date')::timestamptz, scheduled_date),
    scheduled_end_date   = COALESCE((p_updates->>'scheduled_end_date')::timestamptz, scheduled_end_date),
    pilot_id             = COALESCE((p_updates->>'pilot_id')::uuid, pilot_id),
    amount               = COALESCE((p_updates->>'amount')::decimal, amount),
    location             = COALESCE(p_updates->>'location', location),
    google_maps_link     = COALESCE(p_updates->>'google_maps_link', google_maps_link),
    notes                = COALESCE(p_updates->>'notes', notes),
    is_recurring         = COALESCE((p_updates->>'is_recurring')::boolean, is_recurring),
    recurring_pattern    = COALESCE((p_updates->>'recurring_pattern')::recurring_pattern, recurring_pattern),
    is_outsourced        = COALESCE((p_updates->>'is_outsourced')::boolean, is_outsourced)
  WHERE id = p_schedule_id;

  -- Refresh vendors in junction table from p_vendors JSON array (no reliance on schedules columns)
  DELETE FROM public.schedule_vendors WHERE schedule_id = p_schedule_id;

  IF p_vendors IS NOT NULL AND jsonb_typeof(p_vendors) = 'array' AND jsonb_array_length(p_vendors) > 0 THEN
    FOR vendor_record IN SELECT * FROM jsonb_array_elements(p_vendors)
    LOOP
      INSERT INTO public.schedule_vendors (schedule_id, vendor_id, cost, notes)
      VALUES (
        p_schedule_id,
        (vendor_record->>'vendor_id')::uuid,
        NULLIF(vendor_record->>'cost','')::decimal,
        vendor_record->>'notes'
      );
    END LOOP;
  END IF;

  SELECT to_jsonb(s) INTO updated_schedule
  FROM public.schedules s
  WHERE s.id = p_schedule_id;

  RETURN updated_schedule;
END;
$function$;

COMMIT;