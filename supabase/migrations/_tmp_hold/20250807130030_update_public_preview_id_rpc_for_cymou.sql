-- Up: ensure preview RPC can return CYMOU vendor IDs
-- The existing preview RPC maps a validated entity_type to an internal entity_type and fixed prefix,
-- then delegates to cymatics_ids.peek_next_structured_id(mapped_type, mapped_prefix).
-- We mirror the same pattern and add 'vendor' => ('vendor', 'CYMOU').

create or replace function public.peek_next_entity_id(entity_type text)
returns text
language plpgsql
security definer
stable
set search_path = public, cymatics_ids
as $$
declare
  mapped_type text;
  mapped_prefix text;
  result text;
begin
  if entity_type = 'client' then
    mapped_type := 'client';
    mapped_prefix := 'CYMCL';
  elsif entity_type = 'project' then
    mapped_type := 'project';
    mapped_prefix := 'CYMPR';
  elsif entity_type = 'schedule' then
    mapped_type := 'schedule';
    mapped_prefix := 'CYM';
  elsif entity_type = 'vendor' then
    mapped_type := 'vendor';
    mapped_prefix := 'CYMOU';
  else
    raise exception 'invalid entity_type';
  end if;

  select cymatics_ids.peek_next_structured_id(mapped_type, mapped_prefix) into result;
  return result;
end;
$$;

-- Ownership and grants (idempotent)
do $$
begin
  begin
    alter function public.peek_next_entity_id(text) owner to postgres;
  exception
    when insufficient_privilege or invalid_authorization_specification then
      begin
        alter function public.peek_next_entity_id(text) owner to supabase_admin;
      exception
        when others then null;
      end;
  end;
end$$;

grant execute on function public.peek_next_entity_id(text) to authenticated;
do $$
begin
  perform 1 from pg_roles where rolname = 'service_role';
  if found then
    grant execute on function public.peek_next_entity_id(text) to service_role;
  end if;
end$$;

-- Down: restore function without vendor branch (optional). Here we keep vendor support; no-op.