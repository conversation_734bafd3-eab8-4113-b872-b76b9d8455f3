-- Up: backfill outsourcing_vendors IDs deterministically and seed counters for CYMOU
-- Guard: skip safely if table not present on target

DO $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'public' AND c.relname = 'outsourcing_vendors' AND c.relkind = 'r'
  ) THEN
    -- 1) Deterministic backfill for outsourcing_vendors where custom_id is NULL
    WITH v AS (
      SELECT id,
             to_char((COALESCE(created_at, now()) AT TIME ZONE 'Asia/Kolkata'), 'YY') AS yy,
             EXTRACT(YEAR FROM (COALESCE(created_at, now()) AT TIME ZONE 'Asia/Kolkata'))::int AS yr,
             row_number() OVER (
               PARTITION BY EXTRACT(YEAR FROM (COALESCE(created_at, now()) AT TIME ZONE 'Asia/Kolkata'))
               ORDER BY created_at NULLS LAST, id
             ) AS rn
      FROM public.outsourcing_vendors
      WHERE custom_id IS NULL
    )
    UPDATE public.outsourcing_vendors t
    SET custom_id = 'CYMOU-' || v.yy || '-' || lpad(v.rn::text, 3, '0')
    FROM v WHERE t.id = v.id;

    -- 2) Seed/align counters for series 'vendor' based on max observed NNN per year
    INSERT INTO cymatics_ids.counters(entity_type, year, last_number)
    SELECT 'vendor', yr::smallint, max(COALESCE(substring(custom_id from '([0-9]{3})$')::int,0))
    FROM (
      SELECT EXTRACT(YEAR FROM (created_at AT TIME ZONE 'Asia/Kolkata'))::int AS yr, custom_id
      FROM public.outsourcing_vendors
      WHERE custom_id IS NOT NULL
    ) q
    GROUP BY yr
    ON CONFLICT (entity_type, year) DO UPDATE
    SET last_number = GREATEST(cymatics_ids.counters.last_number, EXCLUDED.last_number);
  ELSE
    RAISE NOTICE 'public.outsourcing_vendors not found; skipping CYMOU backfill and counters seeding';
  END IF;
END $$;

-- Down: non-destructive rollback of backfill and counters for outsourcing_vendors
DO $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'public' AND c.relname = 'outsourcing_vendors' AND c.relkind = 'r'
  ) THEN
    EXECUTE 'UPDATE public.outsourcing_vendors SET custom_id = NULL';
  END IF;
END $$;
DELETE FROM cymatics_ids.counters WHERE entity_type IN ('vendor');