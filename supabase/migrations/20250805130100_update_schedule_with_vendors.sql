CREATE OR REPLACE FUNCTION update_schedule_with_vendors(
  p_amount decimal,
  p_google_maps_link text,
  p_is_outsourced boolean,
  p_is_recurring boolean,
  p_location text,
  p_notes text,
  p_pilot_id uuid,
  p_recurring_pattern text,
  p_schedule_id uuid,
  p_scheduled_date timestamptz,
  p_scheduled_end_date timestamptz,
  p_vendors jsonb
)
RETURNS JSONB AS $$
DECLARE
  updated_schedule JSONB;
  vendor_record JSONB;
BEGIN
  -- Update the schedule
  UPDATE schedules
  SET
    scheduled_date = p_scheduled_date,
    scheduled_end_date = p_scheduled_end_date,
    pilot_id = p_pilot_id,
    amount = p_amount,
    location = p_location,
    google_maps_link = p_google_maps_link,
    notes = p_notes,
    is_recurring = p_is_recurring,
    recurring_pattern = p_recurring_pattern::recurring_pattern,
    is_outsourced = p_is_outsourced
  WHERE id = p_schedule_id;

  -- Clear existing vendors and insert new ones
  DELETE FROM schedule_vendors WHERE schedule_id = p_schedule_id;
  IF p_is_outsourced AND jsonb_array_length(p_vendors) > 0 THEN
    FOR vendor_record IN SELECT * FROM jsonb_array_elements(p_vendors)
    LOOP
      INSERT INTO schedule_vendors (schedule_id, vendor_id, cost, notes)
      VALUES (
        p_schedule_id,
        (vendor_record->>'vendor_id')::UUID,
        (vendor_record->>'cost')::DECIMAL,
        vendor_record->>'notes'
      );
    END LOOP;
  END IF;

  -- Return the updated schedule
  SELECT to_jsonb(s) INTO updated_schedule FROM schedules s WHERE s.id = p_schedule_id;
  RETURN updated_schedule;
END;
$$ LANGUAGE plpgsql;