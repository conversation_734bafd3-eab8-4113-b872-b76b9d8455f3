-- Up: create schedules table if missing (no data copy)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'schedules') THEN

    CREATE TABLE public.schedules (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      project_id uuid REFERENCES public.projects(id) ON DELETE SET NULL,
      title text,
      description text,
      scheduled_at timestamptz,
      location text,
      created_at timestamptz NOT NULL DEFAULT now(),
      updated_at timestamptz NOT NULL DEFAULT now()
    );

    ALTER TABLE public.schedules ENABLE ROW LEVEL SECURITY;
  END IF;
END$$;

-- Down: drop schedules table only if it is empty to avoid destructive loss
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'schedules') THEN
    IF NOT EXISTS (SELECT 1 FROM public.schedules LIMIT 1) THEN
      DROP TABLE public.schedules;
    END IF;
  END IF;
END$$;
