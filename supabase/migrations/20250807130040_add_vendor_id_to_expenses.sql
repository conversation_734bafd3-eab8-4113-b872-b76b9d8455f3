-- Migration: Add vendor_id column to expenses table
-- Date: 2025-08-07
-- Description: Add vendor_id column to expenses table to link expenses to vendors

-- Add vendor_id column to expenses table
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS vendor_id UUID REFERENCES outsourcing_vendors(id) ON DELETE SET NULL;

-- Add index for better performance
CREATE INDEX IF NOT EXISTS idx_expenses_vendor_id ON expenses(vendor_id);

-- Add comment to document the change
COMMENT ON COLUMN expenses.vendor_id IS 'Reference to the vendor associated with this expense (for outsourcing expenses)';