-- Up: schema + counters + functions
CREATE SCHEMA IF NOT EXISTS cymatics_ids;
REVOKE ALL ON SCHEMA cymatics_ids FROM PUBLIC;

CREATE TABLE IF NOT EXISTS cymatics_ids.counters (
  entity_type text NOT NULL,
  year smallint NOT NULL,
  last_number integer NOT NULL DEFAULT 0 CHECK (last_number >= 0 AND last_number <= 999),
  updated_at timestamptz NOT NULL DEFAULT now(),
  PRIMARY KEY(entity_type, year)
);
REVOKE ALL ON cymatics_ids.counters FROM PUBLIC;

-- generator
CREATE OR REPLACE FUNCTION cymatics_ids.generate_structured_id(p_entity_type text, p_prefix text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
VOLATILE
SET search_path = cymatics_ids, public
AS $$
DECLARE
  ts timestamptz := now() AT TIME ZONE 'Asia/Kolkata';
  yy text := to_char(ts, 'YY');
  yr smallint := EXTRACT(YEAR FROM ts)::smallint;
  next_num integer;
BEGIN
  INSERT INTO cymatics_ids.counters(entity_type, year, last_number)
  VALUES (p_entity_type, yr, 1)
  ON CONFLICT (entity_type, year)
  DO UPDATE SET last_number = cymatics_ids.counters.last_number + 1,
                updated_at = now()
  RETURNING last_number INTO next_num;

  IF next_num > 999 THEN
    RAISE EXCEPTION 'ID capacity exhausted for % in % (reached %).', p_entity_type, yr, next_num;
  END IF;

  RETURN p_prefix || '-' || yy || '-' || lpad(next_num::text, 3, '0');
END;$$;

-- peek (approximate)
CREATE OR REPLACE FUNCTION cymatics_ids.peek_next_structured_id(p_entity_type text, p_prefix text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
SET search_path = cymatics_ids, public
AS $$
DECLARE
  ts timestamptz := now() AT TIME ZONE 'Asia/Kolkata';
  yy text := to_char(ts, 'YY');
  yr smallint := EXTRACT(YEAR FROM ts)::smallint;
  last_num integer := 0;
  next_num integer;
BEGIN
  SELECT last_number INTO last_num FROM cymatics_ids.counters WHERE entity_type = p_entity_type AND year = yr;
  next_num := COALESCE(last_num, 0) + 1;
  IF next_num > 999 THEN
    RAISE EXCEPTION 'ID capacity would exceed 999 for % in %.', p_entity_type, yr;
  END IF;
  RETURN p_prefix || '-' || yy || '-' || lpad(next_num::text, 3, '0');
END;$$;

-- trigger applier (no declared args; uses TG_ARGV[0]=prefix, TG_ARGV[1]=entity_type)
CREATE OR REPLACE FUNCTION cymatics_ids.apply_structured_id()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = cymatics_ids, public
AS $$
DECLARE
  v_prefix text := TG_ARGV[0];
  v_entity_type text := TG_ARGV[1];
BEGIN
  IF NEW.custom_id IS NULL THEN
    NEW.custom_id := cymatics_ids.generate_structured_id(v_entity_type, v_prefix);
  END IF;
  RETURN NEW;
END;$$;

-- Down
-- Drop new no-arg trigger function first, then legacy signature if present, then other objects
DROP FUNCTION IF EXISTS cymatics_ids.apply_structured_id() CASCADE;
DROP FUNCTION IF EXISTS cymatics_ids.apply_structured_id(prefix text, entity_type text) CASCADE;
DROP FUNCTION IF EXISTS cymatics_ids.peek_next_structured_id(entity_type text, prefix text) CASCADE;
DROP FUNCTION IF EXISTS cymatics_ids.generate_structured_id(entity_type text, prefix text) CASCADE;
DROP TABLE IF EXISTS cymatics_ids.counters;
DROP SCHEMA IF EXISTS cymatics_ids;
