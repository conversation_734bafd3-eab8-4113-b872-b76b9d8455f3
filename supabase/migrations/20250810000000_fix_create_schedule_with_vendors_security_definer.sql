-- Migration: Fix create_schedule_with_vendors to use SECURITY DEFINER
-- Date: 2025-08-10
-- Description: Adds SECURITY DEFINER to create_schedule_with_vendors function to bypass RLS policies

CREATE OR REPLACE FUNCTION public.create_schedule_with_vendors(
  p_project_id UUID,
  p_scheduled_date TIMESTAMPTZ,
  p_scheduled_end_date TIMESTAMPTZ,
  p_pilot_id UUID,
  p_amount DECIMAL,
  p_location TEXT,
  p_google_maps_link TEXT,
  p_notes TEXT,
  p_is_recurring BOOLEAN,
  p_recurring_pattern TEXT,
  p_is_outsourced BOOLEAN,
  p_vendors JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  new_schedule_id UUID;
  new_schedule JSONB;
  vendor_record JSONB;
BEGIN
  INSERT INTO public.schedules (
    project_id, scheduled_date, scheduled_end_date, pilot_id, amount,
    location, google_maps_link, notes, is_recurring, recurring_pattern, is_outsourced
  ) VALUES (
    p_project_id, p_scheduled_date, p_scheduled_end_date, p_pilot_id, p_amount,
    p_location, p_google_maps_link, p_notes, p_is_recurring, p_recurring_pattern::recurring_pattern, p_is_outsourced
  ) RETURNING id INTO new_schedule_id;

  -- Insert vendors into junction table (driven solely by p_vendors)
  IF p_vendors IS NOT NULL AND jsonb_typeof(p_vendors) = 'array' AND jsonb_array_length(p_vendors) > 0 THEN
    FOR vendor_record IN SELECT * FROM jsonb_array_elements(p_vendors)
    LOOP
      INSERT INTO public.schedule_vendors (schedule_id, vendor_id, cost, notes)
      VALUES (
        new_schedule_id,
        (vendor_record->>'vendor_id')::UUID,
        NULLIF(vendor_record->>'cost','')::DECIMAL,
        vendor_record->>'notes'
      );
    END LOOP;
  END IF;

  -- Return the newly created schedule with custom_id included
  SELECT jsonb_build_object(
    'id', s.id,
    'custom_id', s.custom_id,
    'project_id', s.project_id,
    'scheduled_date', s.scheduled_date,
    'scheduled_end_date', s.scheduled_end_date,
    'pilot_id', s.pilot_id,
    'amount', s.amount,
    'location', s.location,
    'google_maps_link', s.google_maps_link,
    'notes', s.notes,
    'is_recurring', s.is_recurring,
    'recurring_pattern', s.recurring_pattern,
    'is_outsourced', s.is_outsourced
  )
  INTO new_schedule
  FROM public.schedules s
  WHERE s.id = new_schedule_id;

  RETURN new_schedule;
END;
$function$;