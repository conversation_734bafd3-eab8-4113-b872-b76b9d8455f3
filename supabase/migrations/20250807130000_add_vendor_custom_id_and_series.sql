-- Up: ensure outsourcing_vendors table has custom_id and functions accept vendors series
-- Guarded to skip cleanly if table does not exist on the target

DO $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'public' AND c.relname = 'outsourcing_vendors' AND c.relkind = 'r'
  ) THEN
    EXECUTE 'ALTER TABLE public.outsourcing_vendors ADD COLUMN IF NOT EXISTS custom_id text';
  ELSE
    RAISE NOTICE 'public.outsourcing_vendors not found; skipping custom_id column addition';
  END IF;
END $$;

-- 2) No allow-list exists inside cymatics_ids functions (they are generic and parameterized by (entity_type, prefix)).
-- Therefore, no changes needed to cymatics_ids.apply_structured_id() / peek/generate functions for accepting CYMOU/vendor.
-- This file intentionally documents that acceptance is via trigger args, not via a hardcoded list.

-- Down (non-destructive): attempt to drop column if table exists
DO $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'public' AND c.relname = 'outsourcing_vendors' AND c.relkind = 'r'
  ) THEN
    EXECUTE 'ALTER TABLE public.outsourcing_vendors DROP COLUMN IF EXISTS custom_id';
  END IF;
END $$;