-- Up: ensure public.outsourcing_vendors has custom_id column (guarded)
DO $
BEGIN
  IF EXISTS (
    SELECT 1
    FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = public AND c.relname = outsourcing_vendors AND c.relkind = r
  ) THEN
    IF NOT EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_schema = public AND table_name = outsourcing_vendors AND column_name = custom_id
    ) THEN
      EXECUTE ALTER TABLE public.outsourcing_vendors ADD COLUMN custom_id text;
    END IF;
  ELSE
    RAISE NOTICE public.outsourcing_vendors not found; skipping column addition;
  END IF;
END
$;

-- Down: drop column if exists (guarded)
DO $
BEGIN
  IF EXISTS (
    SELECT 1
    FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = public AND c.relname = outsourcing_vendors AND c.relkind = r
  ) THEN
    IF EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_schema = public AND table_name = outsourcing_vendors AND column_name = custom_id
    ) THEN
      EXECUTE ALTER TABLE public.outsourcing_vendors DROP COLUMN custom_id;
    END IF;
  END IF;
END
$;
