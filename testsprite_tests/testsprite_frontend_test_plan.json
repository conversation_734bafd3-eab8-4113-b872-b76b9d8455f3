[{"id": "TC001", "title": "Create Client Successfully", "description": "Verify that a new client can be created with valid name and email and is retrievable via API.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Send POST request to /api/clients with valid name and email."}, {"type": "assertion", "description": "Response status is 200 and contains success confirmation."}, {"type": "action", "description": "Send GET request to /api/clients to retrieve the list of clients."}, {"type": "assertion", "description": "New client appears in the returned list with correct data."}]}, {"id": "TC002", "title": "Create Client with Invalid Data", "description": "Verify API handles attempts to create a client with missing or invalid fields gracefully.", "category": "error handling", "priority": "High", "steps": [{"type": "action", "description": "Send POST request to /api/clients with missing name and/or invalid email."}, {"type": "assertion", "description": "Response status indicates error and validation message is returned."}]}, {"id": "TC003", "title": "Retrieve All Projects", "description": "Verify that all projects can be retrieved successfully via API.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Send GET request to /api/projects."}, {"type": "assertion", "description": "Response status is 200 and list of projects is returned."}]}, {"id": "TC004", "title": "Create Schedule with Vendors", "description": "Verify creation of a test schedule linked with vendors and ensure data correctness.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Send GET request to /api/test-createwithvendors."}, {"type": "assertion", "description": "Response status is 200 with success true and schedule object returned."}]}, {"id": "TC005", "title": "Ensure SharePoint Folder Creation for Schedule", "description": "Verify that a SharePoint folder is created successfully for a specific schedule through background job.", "category": "integration", "priority": "High", "steps": [{"type": "action", "description": "Send POST request to /api/ensure-sharepoint-folder with valid scheduleId."}, {"type": "assertion", "description": "Response confirms folder creation success and folder exists in SharePoint via Graph API."}]}, {"id": "TC006", "title": "Background Job Status Retrieval", "description": "Verify that background job status can be requested with jobId and returns correct job state.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Send GET request to /api/background-jobs with a valid jobId query parameter."}, {"type": "assertion", "description": "Response status 200 and job status information is returned properly."}, {"type": "action", "description": "Send GET request to /api/background-jobs without jobId."}, {"type": "assertion", "description": "Returns list of all background jobs."}]}, {"id": "TC007", "title": "Microsoft Graph API Connection Test", "description": "Ensure that connection to Microsoft Graph API is working and test endpoint returns valid data.", "category": "integration", "priority": "High", "steps": [{"type": "action", "description": "Send GET request to /api/test-microsoft-graph."}, {"type": "assertion", "description": "Response returns 200 with expected Graph API test results."}]}, {"id": "TC008", "title": "Vendor Shoots Data Retrieval", "description": "Verify retrieval of vendor shoot schedules via vendor shoots API endpoint.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Send GET request to /api/vendor-shoots."}, {"type": "assertion", "description": "Response contains list of vendor shoots with valid data."}]}, {"id": "TC009", "title": "Google Maps Place Search Returns Valid Results", "description": "Verify that Google Maps API integration returns place search results for valid queries.", "category": "integration", "priority": "High", "steps": [{"type": "action", "description": "Send GET request to /api/places/search with valid 'query' parameter."}, {"type": "assertion", "description": "Response returns list of places matching query with expected fields."}]}, {"id": "TC010", "title": "Role-Based Access Control Restriction", "description": "Verify users with different roles have proper access restrictions and forbidden actions are prevented.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Attempt to perform admin-only actions with non-admin role credentials."}, {"type": "assertion", "description": "System rejects action with appropriate error or access denied response."}, {"type": "action", "description": "Perform allowed actions based on the user role."}, {"type": "assertion", "description": "System permits action according to role permissions."}]}, {"id": "TC011", "title": "File Upload to OneDrive Folder Structure", "description": "Verify that files uploaded are placed automatically in correct OneDrive folder structure by client/project/schedule.", "category": "integration", "priority": "High", "steps": [{"type": "action", "description": "Upload a file for a specific schedule via the platform."}, {"type": "assertion", "description": "File appears in the correct OneDrive folder automatically created for given client/project/schedule."}]}, {"id": "TC012", "title": "Financial Payments Partial and Delayed Handling", "description": "Verify that partial and delayed payments are recorded correctly and reflected in financial tracking and dashboards.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Record a partial payment for a project."}, {"type": "assertion", "description": "Payment status reflects partial payment and remaining balance is computed correctly."}, {"type": "action", "description": "Record a delayed payment with future date."}, {"type": "assertion", "description": "Payment is tracked with delay information and included in dashboards accurately."}]}, {"id": "TC013", "title": "In-App Notifications Triggering and Accuracy", "description": "Verify notifications are triggered timely for upcoming schedules, payment due dates, and overdue tasks.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Create schedule or payment event close to notification trigger time."}, {"type": "assertion", "description": "Notification appears in-app on user dashboard promptly and contains correct details."}]}, {"id": "TC014", "title": "Dashboard KPIs and Drill Down Functionality", "description": "Verify dashboards display active projects, schedules, payments accurately and drill down navigations work.", "category": "ui", "priority": "Medium", "steps": [{"type": "action", "description": "Access dashboard view as authorized user."}, {"type": "assertion", "description": "KPIs displayed match data in system including active projects, schedules, payments."}, {"type": "action", "description": "Click on a KPI to drill down into detailed status or file listings."}, {"type": "assertion", "description": "Drill down view loads relevant detailed data correctly."}]}, {"id": "TC015", "title": "Responsive UI Across Supported Devices", "description": "Verify that the UI renders all key features without critical bugs on desktop, tablet, and mobile screen sizes.", "category": "ui", "priority": "Medium", "steps": [{"type": "action", "description": "Open the application on various devices and screen sizes."}, {"type": "assertion", "description": "UI elements including forms, dashboards, lists render correctly and remain usable."}]}]