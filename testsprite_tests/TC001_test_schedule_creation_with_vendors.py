import requests


def test_create_with_vendors():
    url = "http://localhost:3000/api/test-createwithvendors"
    try:
        resp = requests.get(url, timeout=10)
    except Exception as e:
        raise AssertionError(f"Request to {url} failed: {e}")

    assert resp.status_code == 200, f"Expected status code 200, got {resp.status_code}"

    try:
        data = resp.json()
    except ValueError:
        raise AssertionError("Response is not valid JSON")

    assert isinstance(data, dict), "Response JSON is not an object"

    # Validate 'success' field
    assert 'success' in data, "Missing 'success' field in response"
    assert isinstance(data['success'], bool), "'success' field is not a boolean"

    # Validate 'schedule' field
    assert 'schedule' in data, "Missing 'schedule' field in response"
    assert isinstance(data['schedule'], dict), "'schedule' field is not an object"

    # Validate 'message' field (should be a string if present)
    assert 'message' in data, "Missing 'message' field in response"
    assert data['message'] is None or isinstance(data['message'], str), "'message' field is not a string or null"

    print("TC001 passed: /api/test-createwithvendors returned expected schema")


if __name__ == '__main__':
    test_create_with_vendors()