import json
import urllib.request
import urllib.error


def test_test_createwithvendors():
    url = 'http://localhost:3000/api/test-createwithvendors'
    req = urllib.request.Request(url, method='GET', headers={'Accept': 'application/json'})
    try:
        with urllib.request.urlopen(req, timeout=10) as resp:
            status = resp.getcode()
            body = resp.read().decode('utf-8')
    except urllib.error.HTTPError as e:
        assert False, f'HTTPError: {e.code} {e.reason}'
    except urllib.error.URLError as e:
        assert False, f'URLError: {e.reason}'

    try:
        data = json.loads(body) if body else {}
    except Exception as e:
        assert False, f'Invalid JSON response: {e}'

    assert status == 200, f'Expected status 200, got {status}'
    assert isinstance(data, dict), 'Response JSON must be an object'

    # PRD expects: success (boolean), schedule (object), message (string)
    assert 'success' in data, 'Missing "success" in response'
    assert isinstance(data['success'], bool), '"success" must be boolean'

    assert 'schedule' in data, 'Missing "schedule" in response'
    assert isinstance(data['schedule'], dict), '"schedule" must be an object'

    if 'message' in data:
        assert isinstance(data['message'], str), '"message" must be a string'

    print('Test passed: /api/test-createwithvendors returned valid response')


if __name__ == '__main__':
    test_test_createwithvendors()
