import requests


def test_test_createwithvendors():
    url = "http://127.0.0.1:3000/api/test-createwithvendors"
    resp = requests.get(url, timeout=10)
    assert resp.status_code == 200, f"Expected 200 OK, got {resp.status_code}, body: {resp.text}"
    try:
        data = resp.json()
    except ValueError:
        assert False, f"Response is not JSON: {resp.text}"

    assert isinstance(data, dict), f"Response JSON not an object: {data}"

    # PRD specifies 'success' (boolean), 'schedule' (object) and 'message' (string)
    assert 'success' in data, "Response JSON missing 'success'"
    assert isinstance(data['success'], bool), "'success' should be boolean"

    if 'schedule' in data:
        assert isinstance(data['schedule'], dict), "'schedule' should be an object"

    if 'message' in data:
        assert isinstance(data['message'], str), "'message' should be a string"


if __name__ == '__main__':
    test_test_createwithvendors()
    print('Test passed')