{"meta": {"project": "Cymatics Project Management System", "date": "2025-08-08", "prepared_by": "Generated by TestSprite"}, "product_overview": "Cymatics is a centralized internal web-based project management platform tailored for managing drone service projects. It integrates scheduling, client and vendor management, financial tracking, and file management with Microsoft OneDrive via Microsoft Graph API, alongside mapping features powered by Google Maps API to streamline operations, collaboration, and visibility across all project stages.", "core_goals": ["Centralize all project, client, scheduling, and financial data into a unified platform.", "Automate recurring workflows such as schedule creation, folder organization, and payment calculations.", "Standardize file management with enforced OneDrive folder structures and integration.", "Enhance collaboration through role-based access control and shared real-time data.", "Increase operational visibility via dashboards with KPIs, notifications, and alerts.", "Establish a scalable and extensible architecture for future features like client portals and productivity analytics."], "key_features": ["Dashboard with KPIs (active projects, schedules, payments) and notifications.", "Comprehensive project management including client-linked projects, scheduling (recurring/manual), team assignments, and calendar views.", "Client and vendor management with profiles, project histories, and payment tracking.", "Background job processing for asynchronous tasks like SharePoint folder creation.", "File management fully integrated with Microsoft OneDrive using Microsoft Graph API for automated folder creation and file upload.", "Financial management with expense tracking, partial/delayed payments, and multi-format export options.", "Post-processing workflow tracking with status updates and overdue task alerts.", "Google Maps integration for location selection and place search.", "Role-based access control supporting <PERSON><PERSON>, Manager, Pilot, and Editor roles."], "user_flow_summary": ["Admin or Manager creates and manages clients and projects via forms linking to client databases and project details.", "Users schedule projects with options for recurring or manual dates, assigning pilots and editors as needed.", "Background jobs handle asynchronous tasks such as creating SharePoint folders tied to schedules automatically.", "File uploads are made into structured OneDrive folders automatically generated per client/project/schedule.", "Financial entries like expenses and payments are tracked and calculated with options for partial or delayed payment.", "Users receive in-app notifications for upcoming schedules, payment due dates, and task deadlines.", "Users access dashboards to monitor KPIs and drill down into project statuses and files.", "Different roles interact with the system according to their permissions ensuring proper workflow and data integrity."], "validation_criteria": ["Successful creation, retrieval, and update of clients, projects, schedules, and vendor data via API endpoints.", "Automated background job processing triggers and completes tasks such as SharePoint folder creation without user intervention.", "File management operations correctly create and place files in OneDrive folder structures corresponding to project schedule dates.", "Financial calculations for payments and expenses are accurate and reflected in project dashboards and exports.", "All in-app notifications are triggered timely according to events (schedules, payments, tasks).", "Google Maps API integration returns accurate place search and geocode data for project locations.", "UI displays all key features responsively with no critical bugs across supported devices.", "Role-based access control restricts features according to user roles, preventing unauthorized access."], "code_summary": {"tech_stack": ["TypeScript", "Next.js 15.4.2", "React 19.1.0", "Supabase", "PostgreSQL", "Microsoft Graph API", "Node.js", "Tailwind CSS", "Radix <PERSON>", "Google Maps API"], "features": [{"name": "Schedule Management", "description": "Create, manage and track schedules with SharePoint integration and background job processing", "files": ["src/app/api/test-createwithvendors/route.ts", "src/app/api/ensure-sharepoint-folder/route.ts", "src/app/api/ensure-sharepoint-folder-sync/route.ts", "src/lib/api.ts", "src/lib/background-jobs.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/test-createwithvendors": {"get": {"summary": "Create a test schedule with vendors", "responses": {"200": {"description": "Schedule created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "schedule": {"type": "object"}, "message": {"type": "string"}}}}}}}}}, "/api/ensure-sharepoint-folder": {"post": {"summary": "Create SharePoint folder for a specific schedule", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"scheduleId": {"type": "string"}}}}}}}}}}}, {"name": "Client Management", "description": "Manage clients and their associated projects", "files": ["src/app/api/clients/route.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/clients": {"get": {"summary": "Get all clients", "responses": {"200": {"description": "List of clients"}}}, "post": {"summary": "Create a new client", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}}}}}}}}}}}, {"name": "Project Management", "description": "Manage projects and their lifecycle", "files": ["src/app/api/projects/route.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/projects": {"get": {"summary": "Get all projects", "responses": {"200": {"description": "List of projects"}}}}}}}, {"name": "Background Jobs System", "description": "Handle asynchronous tasks like SharePoint folder creation", "files": ["src/app/api/background-jobs/route.ts", "src/lib/background-jobs.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/background-jobs": {"get": {"summary": "Get background job status", "parameters": [{"name": "jobId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Job status or list of all jobs"}}}}}}}, {"name": "SharePoint Integration", "description": "Microsoft Graph API integration for SharePoint folder management", "files": ["src/lib/microsoft-graph.ts", "src/lib/sharepoint-service.ts", "src/app/api/test-microsoft-graph/route.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/test-microsoft-graph": {"get": {"summary": "Test Microsoft Graph API connection", "responses": {"200": {"description": "Graph API test results"}}}}}}}, {"name": "Vendor Management", "description": "Manage vendors and their shoots/schedules", "files": ["src/app/api/vendor-shoots/route.ts", "src/app/api/test-vendors/route.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/vendor-shoots": {"get": {"summary": "Get vendor shoots", "responses": {"200": {"description": "List of vendor shoots"}}}}}}}, {"name": "Places and Maps Integration", "description": "Google Maps API integration for location services", "files": ["src/app/api/places/search/route.ts", "src/app/api/places/reverse-geocode/route.ts", "src/app/api/places/details/route.ts", "src/lib/google-maps-utils.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/places/search": {"get": {"summary": "Search for places using Google Maps API", "parameters": [{"name": "query", "in": "query", "schema": {"type": "string"}}]}}}}}]}}