{"tech_stack": ["TypeScript", "Next.js 15.4.2", "React 19.1.0", "Supabase", "PostgreSQL", "Microsoft Graph API", "Node.js", "Tailwind CSS", "Radix <PERSON>", "Google Maps API"], "features": [{"name": "Schedule Management", "description": "Create, manage and track schedules with SharePoint integration and background job processing", "files": ["src/app/api/test-createwithvendors/route.ts", "src/app/api/ensure-sharepoint-folder/route.ts", "src/app/api/ensure-sharepoint-folder-sync/route.ts", "src/lib/api.ts", "src/lib/background-jobs.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/test-createwithvendors": {"get": {"summary": "Create a test schedule with vendors", "responses": {"200": {"description": "Schedule created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "schedule": {"type": "object"}, "message": {"type": "string"}}}}}}}}}, "/api/ensure-sharepoint-folder": {"post": {"summary": "Create SharePoint folder for a specific schedule", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"scheduleId": {"type": "string"}}}}}}}}}}}, {"name": "Client Management", "description": "Manage clients and their associated projects", "files": ["src/app/api/clients/route.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/clients": {"get": {"summary": "Get all clients", "responses": {"200": {"description": "List of clients"}}}, "post": {"summary": "Create a new client", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}}}}}}}}}}}, {"name": "Project Management", "description": "Manage projects and their lifecycle", "files": ["src/app/api/projects/route.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/projects": {"get": {"summary": "Get all projects", "responses": {"200": {"description": "List of projects"}}}}}}}, {"name": "Background Jobs System", "description": "Handle asynchronous tasks like SharePoint folder creation", "files": ["src/app/api/background-jobs/route.ts", "src/lib/background-jobs.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/background-jobs": {"get": {"summary": "Get background job status", "parameters": [{"name": "jobId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Job status or list of all jobs"}}}}}}}, {"name": "SharePoint Integration", "description": "Microsoft Graph API integration for SharePoint folder management", "files": ["src/lib/microsoft-graph.ts", "src/lib/sharepoint-service.ts", "src/app/api/test-microsoft-graph/route.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/test-microsoft-graph": {"get": {"summary": "Test Microsoft Graph API connection", "responses": {"200": {"description": "Graph API test results"}}}}}}}, {"name": "Vendor Management", "description": "Manage vendors and their shoots/schedules", "files": ["src/app/api/vendor-shoots/route.ts", "src/app/api/test-vendors/route.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/vendor-shoots": {"get": {"summary": "Get vendor shoots", "responses": {"200": {"description": "List of vendor shoots"}}}}}}}, {"name": "Places and Maps Integration", "description": "Google Maps API integration for location services", "files": ["src/app/api/places/search/route.ts", "src/app/api/places/reverse-geocode/route.ts", "src/app/api/places/details/route.ts", "src/lib/google-maps-utils.ts"], "api_doc": {"openapi": "3.0.0", "paths": {"/api/places/search": {"get": {"summary": "Search for places using Google Maps API", "parameters": [{"name": "query", "in": "query", "schema": {"type": "string"}}]}}}}}]}