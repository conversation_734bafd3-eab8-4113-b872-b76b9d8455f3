[{"id": "TC001", "title": "test_schedule_creation_with_vendors", "description": "Verify that the API endpoint /api/test-createwithvendors successfully creates a schedule with associated vendors and returns a 200 status code with a success message."}, {"id": "TC002", "title": "test_create_sharepoint_folder_for_schedule", "description": "Verify that the API endpoint /api/ensure-sharepoint-folder creates a SharePoint folder for a given scheduleId, returns a 200 status code, and confirms successful folder creation."}, {"id": "TC003", "title": "test_background_job_queue_status_retrieval", "description": "Verify that the API endpoint /api/background-jobs returns the current status of the background job queue with a 200 status code and accurate job information."}, {"id": "TC004", "title": "test_create_new_client", "description": "Verify that the API endpoint /api/clients successfully creates a new client when provided with valid client data and returns a 200 status code with a confirmation message."}, {"id": "TC005", "title": "test_get_vendor_shoots_by_vendor_id", "description": "Verify that the API endpoint /api/vendor-shoots retrieves vendor shoots filtered by the provided vendorId query parameter and returns a 200 status code with the correct data."}]