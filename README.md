This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Structured IDs

Cymatics uses structured, human-readable IDs for key entities. These IDs are generated in the database for correctness and uniqueness, and appear in the UI as the “custom_id”.

- Client IDs: CYMCL-YY-NNN (prefix CYMCL), e.g., CYMCL-25-007
- Project IDs: CYMPR-YY-NNN (prefix CYMPR), e.g., CYMPR-25-042
- Schedule IDs: CYM-YY-NNN (prefix CYM), e.g., CYM-25-123

Where:
- YY is the two-digit year based on the current server time
- NNN is a zero-padded counter from 001 to 999

Generation mechanics:
- IDs are assigned by database-side triggers/functions at insert time.
- A concurrency-safe counter is maintained per (entity_type, year) to avoid collisions across parallel inserts.
- Counters reset annually; the first entity of a year receives NNN = 001.

Preview behavior:
- The UI can show an “ID (preview)” for create forms using a dedicated RPC that peeks at the next counter without reserving it.
- Previews are approximate and non-reserving; actual inserts may consume the next number and can differ if there are concurrent inserts in between.
- The preview RPC: public.peek_next_entity_id(entity_type text) returns text, internally mapping:
  - 'client' -> ('client','CYMCL')
  - 'project' -> ('project','CYMPR')
  - 'schedule' -> ('schedule','CYM')
  and defers to cymatics_ids.peek_next_structured_id(mapped_entity_type, mapped_prefix).

Year rollover and capacity:
- Range is 001–999 per entity per year. When capacity is exhausted, inserts will raise an overflow error. Admins should plan archival/backfill or widen capacity if needed.

Operational notes:
- Uniqueness is enforced at the database layer alongside the trigger-based generation to prevent duplicates.
- Existing data may be backfilled with custom_id values using a maintenance script or SQL; ensure uniqueness constraints are respected.
- All migrations related to structured IDs and the preview RPC live under supabase/migrations/. Look for files with create_..._rpc or cymatics_ids in their names.

Quick SQL snippets:
- Peek next for a project (requires authenticated/service role):
  select public.peek_next_entity_id('project');
- Simulate an insert and view generated ID:
  insert into projects (name, client_id) values ('Demo', '...') returning id, custom_id;
- Inspect a few rows:
  select id, custom_id, created_at from clients order by created_at desc limit 5;

Privacy and security:
- Raw counters and generator internals are private; the preview RPC is SECURITY DEFINER and restricted to authenticated (and service role where applicable). Anonymous access is not granted.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Structured IDs Overview

Cymatics uses deterministic, human-readable structured IDs with series-prefix, year, and a zero-padded per-year counter. These IDs are:
- Authoritatively generated in the database via BEFORE INSERT triggers
- Guaranteed unique per entity
- Optimized for sequential readability and operational workflows

### Series implemented
- CYMCL-YY-NNN for Clients
- CYMPR-YY-NNN for Projects
- CYM-YY-NNN for Schedules
- CYMOU-YY-NNN for Outsourcing Vendors (new)

Where:
- Prefix is a fixed uppercase series code (e.g., CYMOU)
- YY is the last two digits of the year in Asia/Kolkata timezone
- NNN is a zero-padded counter from 001 to 999 that resets annually per series

### Generation and Preview
- Authoritative generation:
  - Done by database triggers using a shared function that atomically increments cymatics_ids.counters.
  - Applies on INSERT (and earlier during backfill via UPDATE triggers).
  - Ensures uniqueness with constraints and indices.
- Non-authoritative preview:
  - RPC: public.peek_next_entity_id(entity_type text) returns an approximate next ID without reserving it.
  - UI calls this on create forms and shows “ID (preview): …”.
  - Because it’s a peek, concurrency may produce different final IDs on insert.

### Vendor IDs (CYMOU-YY-NNN)
- Series prefix: CYMOU
- Year: last two digits (Asia/Kolkata)
- Counter: 001..999 per year
- Table: public.outsourcing_vendors
- Generated by trigger: cymatics_ids.apply_structured_id('CYMOU','vendor') on public.outsourcing_vendors
- Uniqueness: enforced by a unique index/constraint on outsourcing_vendors.custom_id
- Format check: outsourcing_vendors.custom_id ~ '^CYMOU-[0-9]{2}-[0-9]{3}$'
- Null behavior:
  - During data migration/backfill: IDs are assigned deterministically.
  - After backfill: NOT NULL is enforced on outsourcing_vendors.custom_id
- Annual reset: the per-year counter resets automatically per series via cymatics_ids.counters

### Database details
- Schema: cymatics_ids with table counters(entity_type text, year smallint/int, last_number int, updated_at timestamptz)
- Core functions:
  - cymatics_ids.apply_structured_id() — trigger function reading TG_ARGV[0]=prefix, TG_ARGV[1]=entity_type
  - cymatics_ids.generate_structured_id(entity_type, prefix) — allocates next ID atomically
  - cymatics_ids.peek_next_structured_id(entity_type, prefix) — computes next ID without reserving
- Preview RPC:
  - public.peek_next_entity_id(entity_type text) maps allowed values to correct series/prefix:
    - 'client' => ('client', 'CYMCL')
    - 'project' => ('project', 'CYMPR')
    - 'schedule' => ('schedule', 'CYM')
    - 'vendor' => ('vendor', 'CYMOU')
- Migrations:
  - Vendors series setup lives in:
    - supabase/migrations/20250807130000_add_vendor_custom_id_and_series.sql
    - supabase/migrations/20250807130010_backfill_vendor_ids_and_seed_counters.sql
    - supabase/migrations/20250807130020_vendor_triggers_and_constraints.sql
    - supabase/migrations/20250807130030_update_public_preview_id_rpc_for_cymou.sql

### Frontend usage
- Preview helper:
  - peekNextId('vendor') or previewNextVendorId() from src/lib/api.ts for UI display
- UI:
  - Vendor create form shows non-blocking “ID (preview): …” and never persists it directly.
  - The inserted row receives the authoritative ID from the DB trigger and the UI uses that returned value.
- API selections:
  - vendorsApi.select lists custom_id explicitly to ensure the field is returned to the UI after creation and on fetch.

### Concurrency and annual reset
- Counters reset per year per series.
- Atomic increments ensure uniqueness.
- Preview is advisory only; final ID may differ under contention.

### Quick checks (SQL)
- Peek next Vendor ID:
  select public.peek_next_entity_id('vendor');
- Verify latest vendors with generated IDs:
  select id, custom_id, created_at from public.outsourcing_vendors order by created_at desc limit 5;
