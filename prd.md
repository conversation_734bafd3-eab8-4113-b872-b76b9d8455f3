# **Product Specification: Cymatics Project Management System**

## **1. Introduction**

**Product Name:** Cymatics – Internal Project Management System

**Vision:**
To create a centralized, intelligent, and intuitive platform for managing the entire lifecycle of Cymatics Drone Services projects — from booking and scheduling to file delivery and payment tracking — while ensuring smooth team collaboration and full operational visibility.

**Overview:**
Cymatics is a **web-based internal management platform** designed to handle drone project bookings, recurring and manual scheduling, resource allocation, OneDrive-based file management, payment tracking, and post-processing task monitoring. It features **role-based access control**, offline-first data entry for key workflows, and integration with Microsoft Graph API for file organization.

---

## **2. Goals and Objectives**

* **Centralize Operations:** Bring all project, client, and financial data into one platform.
* **Automate Workflows:** Automatically handle recurring schedule creation, folder creation, and payment calculations.
* **Streamline File Handling:** Enforce consistent OneDrive folder structures and link files directly in project records.
* **Enhance Collaboration:** Ensure pilots, editors, managers, and admins work from the same source of truth.
* **Boost Visibility:** Provide real-time dashboards and notifications for deadlines, pending payments, and upcoming schedules.
* **Future-Proof:** Build an architecture that allows easy integration of client portals, NAS backups, and productivity tracking.

---

## **3. Target Users**

* **Admin:** Full control over system, user management, all project and financial data.
* **Manager:** Full access to projects, scheduling, payments, and file uploads (no user account control).
* **Pilot:** Can view assigned projects, update schedule progress, upload raw files.
* **Editor:** Can view assigned editing tasks, upload outputs, update post-processing status.

---

## **4. Features and Functionality**

### **4.1 Dashboard**

* KPIs: Active projects, upcoming schedules, pending payments, overdue edits.
* Quick Actions: New Project, Add Expense, Upload Files.
* Notification panel for reminders and alerts.

---

### **4.2 Project & Scheduling**

* **New Project Form:**

  * Client name (linked to client database).
  * Project name.
  * Project type (Wedding, Corporate, Real Estate, Government, Event, etc.).
  * Location (Google Maps link).
  * GST / Non-GST selection.
  * Schedule dates: recurring or manual.
  * Assigned Pilot(s) & Editor(s).
* **Project Details View:**

  * Timeline of schedules, edits, deliveries.
  * Linked OneDrive folders.
  * Payment status and pending tasks.
* **Calendar View:**

  * Monthly/weekly/daily views with color-coded schedules.
  * Drill-down to project details.

---

### **4.3 Vendor & Client Management**

* Client profiles with project history.
* Vendor profiles with payment history and contact details.
* Outsourced vendor tracking with custom IDs.

---

### **4.4 File Management (OneDrive Integration)**

* Automatic folder structure creation:
  `/Client/Project/ScheduleDate/Raw/`
  `/Client/Project/ScheduleDate/Output/`
* Upload to correct folder from app.
* Shareable file links in project view.

---

### **4.5 Financial Management**

* Expense tracking with categories.
* Partial and delayed payment support.
* Auto-calculation of vendor payments based on schedules and rates.
* Export: CSV, Excel, PDF.

---

### **4.6 Post-Processing Workflow**

* Task tracking for editing, mapping, motion tracking, etc.
* Manual status updates: In Progress / Completed / Delivered.
* Overdue task alerts.

---

### **4.7 Notifications**

* Upcoming schedule reminders (24 hrs & 1 hr before).
* Payment due reminders.
* Task deadline alerts.
* In-app only (push & email planned for future).

---

## **5. Technical Requirements**

* **Frontend:** Next.js + React + TypeScript + Tailwind CSS.
* **Backend:** Node.js API routes with PostgreSQL (Supabase).
* **Authentication:** Supabase Auth (RBAC).
* **File Storage:** Microsoft OneDrive (Microsoft Graph API).
* **Hosting:** Vercel for frontend, Supabase hosting for backend.
* **Maps:** Google Maps API.

---

## **6. Design & UX**

* Modern, minimal UI with a **card-based list layout** for projects.
* Fully responsive (desktop, tablet, mobile).
* Accessibility: WCAG 2.1 AA compliant.

---

## **7. Non-Functional Requirements**

* **Performance:** Page load under 3 seconds on 4G; file upload up to 20GB/session.
* **Scalability:** Support 500+ active projects without performance drop.
* **Security:** HTTPS, data encryption, RBAC.
* **Reliability:** 99.9% uptime.

---

## **8. Constraints**

* Primary file storage is Microsoft OneDrive (no other providers in v1).
* Requires internet for full functionality; offline entry supported for expenses & projects only.

---

## **9. Roadmap**

**Phase 1 (2 months):** Dashboard, Project Mgmt, Calendar, OneDrive integration, Payments, Expense tracking.
**Phase 2 (+1 month):** Notifications, Post-processing tracking, Export options.
**Phase 3 (Future):** Client Portal, NAS backup, Push/email notifications, Productivity analytics.

---
