# Cymatics Heatmap Automation - Implementation Guide

## Overview

The Cymatics Heatmap Automation system automatically extracts and organizes geolocation metadata from drone files (photos, videos, SRT) uploaded to SharePoint, generating a `geo_metadata.json` file for direct rendering in the Cymatics app heatmap view.

## System Architecture

### Components

1. **Heatmap Automation Engine** (`src/lib/heatmap-automation.ts`)
   - File organization and metadata extraction
   - SharePoint integration via Microsoft Graph API
   - Parallel processing for performance optimization

2. **Background Job System** (`src/lib/background-jobs.ts`)
   - Asynchronous processing of heatmap automation tasks
   - Retry logic and error handling

3. **API Endpoints**
   - `/api/schedules/[id]/heatmap-automation` - Manual trigger and status
   - `/api/test-heatmap` - System testing and validation

4. **UI Components**
   - `HeatmapViewer` - Interactive data visualization
   - Map page integration with tabbed interface

## File Processing Flow

### 1. Trigger Conditions
- **Automatic**: When a "File Upload" task is marked as completed
- **Manual**: Via API endpoint or UI button

### 2. File Organization
```
Schedule Folder/
├── Raw/
│   ├── Photos/     (.jpg, .jpeg, .png)
│   ├── Videos/     (.mp4, .mov)
│   ├── SRT/        (.srt)
│   └── LRF/        (.lrf - ignored for heatmap)
└── geo_metadata.json
```

### 3. Metadata Extraction

#### Photos (EXIF GPS)
- Uses `exifr` library for EXIF data extraction
- Extracts latitude, longitude, and timestamp
- Processes in parallel batches of 10 for performance

#### Videos (SRT GPS)
- Matches video files with corresponding SRT files
- Supports multiple GPS coordinate formats:
  - `GPS(lat,lng)` or `GPS: lat, lng`
  - `LAT: xx.xxx LON: yy.yyy`
  - `[GPS] lat,lng` (DJI format)
  - `HOME(lat,lng)`
  - `POS: lat lng`

### 4. Output Format
```json
[
  {
    "type": "photo",
    "filename": "IMG_001.jpg",
    "lat": 19.0596,
    "lng": 72.8295,
    "timestamp": "2025-08-12T14:00:00Z"
  },
  {
    "type": "video",
    "filename": "VID_001.mp4",
    "start": {"lat": 19.0596, "lng": 72.8295},
    "end": {"lat": 19.0600, "lng": 72.8300}
  }
]
```

## Configuration

### Environment Variables
```env
# Microsoft Graph API (Required)
MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret
MICROSOFT_TENANT_ID=your_microsoft_tenant_id

# SharePoint Configuration (Hardcoded in constants)
SHAREPOINT_SITE_URL=https://zn6bn.sharepoint.com/sites/files
DRIVE_ID=b!6BliBHB7rkqGeijn571F56igGQK5UQ9GkI_AIFbTSvvooGrFp94hQr6gPCGK1yYV
```

### Database Schema
```sql
-- Added to schedules table
ALTER TABLE schedules 
ADD COLUMN geo_metadata_link TEXT;

CREATE INDEX idx_schedules_geo_metadata_link ON schedules(geo_metadata_link);
```

## API Reference

### POST `/api/schedules/[id]/heatmap-automation`
Triggers heatmap automation for a specific schedule.

**Authentication**: Required (admin, manager, or pilot role)

**Response**:
```json
{
  "success": true,
  "message": "Heatmap automation queued successfully",
  "jobId": "heatmap_automation_schedule_123_1234567890",
  "schedule": {
    "id": "123",
    "custom_id": "CYM001",
    "project": "Project Name",
    "client": "Client Name"
  }
}
```

### GET `/api/schedules/[id]/heatmap-automation`
Gets heatmap automation status for a schedule.

**Response**:
```json
{
  "schedule": {
    "id": "123",
    "custom_id": "CYM001",
    "project": "Project Name",
    "client": "Client Name",
    "sharepoint_folder_id": "folder_id",
    "geo_metadata_link": "https://sharepoint.com/link",
    "has_sharepoint_folder": true,
    "has_geo_metadata": true
  }
}
```

### GET `/api/test-heatmap`
System health check and testing endpoint.

**Response**:
```json
{
  "success": true,
  "tests": {
    "envVars": true,
    "functions": true,
    "database": true
  },
  "system": {
    "availableSchedules": 5,
    "sampleSchedules": [...]
  }
}
```

## Performance Optimization

### Parallel Processing
- Photos processed in batches of 10 simultaneously
- Videos processed in parallel with their SRT files
- Target: <20 seconds for 100 files

### Error Handling
- Individual file failures don't stop the entire process
- Comprehensive logging for debugging
- Retry logic in background job system

### Memory Management
- Files processed in-memory without full download when possible
- Batch processing prevents memory overflow
- Automatic cleanup of temporary data

## Usage Guide

### For Developers

1. **Setup Environment Variables**
   ```bash
   cp .env.example .env.local
   # Fill in Microsoft Graph API credentials
   ```

2. **Test the System**
   ```bash
   curl http://localhost:3000/api/test-heatmap
   ```

3. **Trigger Automation**
   ```bash
   curl -X POST http://localhost:3000/api/schedules/SCHEDULE_ID/heatmap-automation
   ```

### For Users

1. **Upload Files**: Upload drone files to a schedule's SharePoint folder
2. **Complete Task**: Mark the "File Upload" task as completed
3. **View Results**: Check the Map page → Heatmap Data tab
4. **Download Data**: Use the download button to get the JSON file

## Troubleshooting

### Common Issues

1. **"Schedule SharePoint folder not created yet"**
   - Ensure the schedule has a SharePoint folder created
   - Check background job status for folder creation

2. **"No GPS data found"**
   - Verify files contain GPS metadata
   - Check SRT file format compatibility
   - Ensure photos have EXIF GPS data

3. **"Failed to fetch geo metadata"**
   - Check SharePoint permissions
   - Verify the public share link is accessible
   - Check network connectivity

### Debug Steps

1. **Check Logs**: Monitor console output for detailed error messages
2. **Test API**: Use the `/api/test-heatmap` endpoint
3. **Verify Files**: Ensure uploaded files have GPS metadata
4. **Check Permissions**: Verify Microsoft Graph API permissions

## File Format Support

### Supported Photo Formats
- `.jpg`, `.jpeg` - EXIF GPS data
- `.png` - EXIF GPS data (if present)

### Supported Video Formats
- `.mp4` - Requires matching SRT file
- `.mov` - Requires matching SRT file

### SRT File Requirements
- Must contain GPS coordinates in supported formats
- Should match video filename (e.g., `video.mp4` → `video.srt`)
- UTF-8 encoding recommended

## Security Considerations

- Microsoft Graph API uses Client Credentials flow
- SharePoint files accessed via service account
- Public share links created for geo_metadata.json only
- User authentication required for API access
- Role-based access control (admin, manager, pilot)

## Future Enhancements

1. **Real-time Processing**: WebSocket updates for live progress
2. **Advanced Formats**: Support for more drone file formats
3. **Map Integration**: Direct Google Maps/Mapbox integration
4. **Batch Operations**: Process multiple schedules simultaneously
5. **Analytics**: Flight path analysis and statistics
6. **Export Options**: KML, GPX export formats

## Dependencies

```json
{
  "exifr": "^7.1.3",
  "srt-parser-2": "^1.2.3"
}
```

## Testing

Run the test suite:
```bash
# System health check
GET /api/test-heatmap

# Process specific schedule
POST /api/test-heatmap
{
  "scheduleId": "your-schedule-id"
}
```

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review console logs for detailed errors
3. Use the test endpoints to validate system health
4. Verify environment configuration