# Heatmap Automation Feature

## Overview

The Heatmap Automation feature automatically processes uploaded files from drone shoots to extract GPS coordinates and metadata, organizing them into a structured format for easy visualization and analysis.

## How It Works

### 1. Trigger Mechanism
- **Automatic Trigger**: When a "File Upload" task is marked as completed
- **Manual Trigger**: Via API endpoint `/api/schedules/[id]/heatmap-automation`

### 2. Processing Pipeline

1. **File Discovery**: Scans the schedule's SharePoint folder for uploaded files
2. **Metadata Extraction**:
   - **Images**: Extracts GPS coordinates from EXIF data using `exifr` library
   - **SRT Files**: Parses GPS coordinates from DJI drone SRT subtitle files using `srt-parser-2`
   - **Videos**: Attempts to extract GPS from metadata
3. **Data Organization**: Creates a structured `geo_metadata.json` file
4. **SharePoint Integration**: Uploads the metadata file and creates a public share link
5. **Database Update**: Stores the share link in the `schedules.geo_metadata_link` column

### 3. Generated Output

The system creates a `geo_metadata.json` file with the following structure:

```json
{
  "schedule_id": "uuid",
  "generated_at": "2025-01-15T10:30:00Z",
  "total_files_processed": 25,
  "files_with_gps": 18,
  "gps_coordinates": [
    {
      "filename": "DJI_0001.JPG",
      "type": "image",
      "latitude": 37.7749,
      "longitude": -122.4194,
      "altitude": 120.5,
      "timestamp": "2025-01-15T09:15:30Z",
      "source": "exif"
    },
    {
      "filename": "DJI_0001.SRT",
      "type": "srt",
      "coordinates": [
        {
          "latitude": 37.7750,
          "longitude": -122.4195,
          "altitude": 125.0,
          "timestamp": "2025-01-15T09:15:35Z"
        }
      ],
      "source": "srt_parsing"
    }
  ],
  "summary": {
    "center_point": {
      "latitude": 37.7749,
      "longitude": -122.4194
    },
    "bounding_box": {
      "north": 37.7755,
      "south": 37.7743,
      "east": -122.4188,
      "west": -122.4200
    },
    "altitude_range": {
      "min": 115.0,
      "max": 130.0
    }
  }
}
```

## Implementation Details

### Core Files

1. **`/src/lib/heatmap-automation.ts`**
   - Main automation logic
   - File processing and metadata extraction
   - SharePoint integration

2. **`/src/lib/background-jobs.ts`**
   - Background job queue management
   - Heatmap automation job execution

3. **`/src/app/api/tasks/[id]/route.ts`**
   - Task completion API with automation trigger

4. **`/src/app/api/schedules/[id]/heatmap-automation/route.ts`**
   - Manual trigger endpoint
   - Status checking endpoint

### Database Schema

```sql
-- Added to schedules table
ALTER TABLE schedules 
ADD COLUMN IF NOT EXISTS geo_metadata_link TEXT;
```

### Dependencies

- `exifr`: EXIF data extraction from images
- `piexifjs`: Additional EXIF processing capabilities
- `srt-parser-2`: SRT subtitle file parsing for GPS data

## API Endpoints

### Manual Trigger
```http
POST /api/schedules/{schedule_id}/heatmap-automation
```

**Response:**
```json
{
  "success": true,
  "message": "Heatmap automation queued successfully",
  "jobId": "job_uuid",
  "schedule": {
    "id": "schedule_uuid",
    "custom_id": "CYM-SCH-001",
    "project": "Project Name",
    "client": "Client Name"
  }
}
```

### Status Check
```http
GET /api/schedules/{schedule_id}/heatmap-automation
```

**Response:**
```json
{
  "schedule": {
    "id": "schedule_uuid",
    "custom_id": "CYM-SCH-001",
    "project": "Project Name",
    "client": "Client Name",
    "sharepoint_folder_id": "folder_id",
    "geo_metadata_link": "https://sharepoint.com/...",
    "has_sharepoint_folder": true,
    "has_geo_metadata": true
  }
}
```

## Usage Instructions

### For Pilots (Automatic)
1. Complete your drone shoot
2. Upload files to the project's SharePoint folder
3. Mark the "File Upload" task as completed
4. The system automatically processes files and generates the heatmap data

### For Managers (Manual)
1. Navigate to the schedule details
2. Use the heatmap automation API endpoint
3. Monitor the background job status
4. Access the generated metadata via the share link

## Error Handling

The system includes comprehensive error handling for:
- Missing SharePoint folders
- Invalid file formats
- Network connectivity issues
- Authentication failures
- Malformed GPS data

All errors are logged and reported through the background job system.

## Security Considerations

- All file access is authenticated through Microsoft Graph API
- SharePoint permissions are respected
- Generated share links follow SharePoint security policies
- No sensitive data is exposed in metadata files

## Performance Notes

- Processing runs asynchronously to avoid blocking the UI
- Large file sets are processed in batches
- Memory usage is optimized for handling multiple files
- Background jobs include retry mechanisms for reliability

## Future Enhancements

1. **Real-time Processing**: Process files as they're uploaded
2. **Advanced Analytics**: Generate flight path analysis and statistics
3. **Map Integration**: Direct integration with mapping services
4. **Batch Processing**: Handle multiple schedules simultaneously
5. **Custom Filters**: Allow filtering by date, altitude, or location