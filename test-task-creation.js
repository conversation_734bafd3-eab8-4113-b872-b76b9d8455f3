// Test script to verify task creation functionality
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://qxgfulribhhohmggyroq.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4Z2Z1bHJpYmhob2htZ2d5cm9xIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzAwODEyOSwiZXhwIjoyMDY4NTg0MTI5fQ.Uw77zE5XbMWyNnFpTeh_neOb3xmKLss8lbDgkjw3zoU'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testTaskCreation() {
  try {
    console.log('🧪 Testing task creation functionality...')
    
    // 1. Check if users exist with proper roles
    console.log('\n1. Checking user roles...')
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, name, email, role')
      .order('created_at')
    
    if (usersError) {
      throw new Error(`Failed to fetch users: ${usersError.message}`)
    }
    
    console.log('Users found:', users.length)
    const usersByRole = {
      pilot: users.filter(u => u.role === 'pilot'),
      editor: users.filter(u => u.role === 'editor'),
      admin: users.filter(u => u.role === 'admin' || u.role === 'manager')
    }
    
    console.log('Users by role:')
    console.log(`  - Pilots: ${usersByRole.pilot.length}`)
    console.log(`  - Editors: ${usersByRole.editor.length}`)
    console.log(`  - Admins: ${usersByRole.admin.length}`)
    
    if (usersByRole.pilot.length === 0 && usersByRole.editor.length === 0 && usersByRole.admin.length === 0) {
      console.log('❌ No users with proper roles found. Task creation will fail.')
      return
    }
    
    // 2. Check if clients exist
    console.log('\n2. Checking clients...')
    const { data: clients, error: clientsError } = await supabase
      .from('clients')
      .select('id, name, client_type')
      .limit(5)
    
    if (clientsError) {
      throw new Error(`Failed to fetch clients: ${clientsError.message}`)
    }
    
    console.log('Clients found:', clients.length)
    if (clients.length === 0) {
      console.log('❌ No clients found. Creating a test client...')
      
      const { data: testClient, error: createClientError } = await supabase
        .from('clients')
        .insert({
          name: 'Test Task Creation Client',
          email: '<EMAIL>',
          phone: '1234567890',
          client_type: 'Wedding',
          custom_id: `TEST-TASK-${Date.now()}`
        })
        .select()
        .single()
      
      if (createClientError) {
        throw new Error(`Failed to create test client: ${createClientError.message}`)
      }
      
      clients.push(testClient)
      console.log('✅ Test client created:', testClient.name)
    }
    
    // 3. Check if projects exist
    console.log('\n3. Checking projects...')
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id, name, client_id, clients!inner(client_type)')
      .limit(5)
    
    if (projectsError) {
      throw new Error(`Failed to fetch projects: ${projectsError.message}`)
    }
    
    console.log('Projects found:', projects.length)
    let testProject = projects[0]
    
    if (!testProject) {
      console.log('❌ No projects found. Creating a test project...')
      
      const { data: newProject, error: createProjectError } = await supabase
        .from('projects')
        .insert({
          name: 'Test Task Creation Project',
          client_id: clients[0].id,
          status: 'planning',
          custom_id: `TEST-PROJ-TASK-${Date.now()}`
        })
        .select('id, name, client_id, clients!inner(client_type)')
        .single()
      
      if (createProjectError) {
        throw new Error(`Failed to create test project: ${createProjectError.message}`)
      }
      
      testProject = newProject
      console.log('✅ Test project created:', testProject.name)
    }
    
    // 4. Test schedule creation (which should create tasks)
    console.log('\n4. Testing schedule creation with task creation...')
    
    const scheduleData = {
      project_id: testProject.id,
      scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      location: 'Test Location',
      status: 'scheduled',
      custom_id: `TEST-SCHED-${Date.now()}`
    }
    
    console.log('Creating schedule:', scheduleData)
    
    const { data: schedule, error: scheduleError } = await supabase
      .from('schedules')
      .insert(scheduleData)
      .select(`
        *,
        project:projects(
          id, name, client_id,
          clients!inner(client_type)
        )
      `)
      .single()
    
    if (scheduleError) {
      throw new Error(`Failed to create schedule: ${scheduleError.message}`)
    }
    
    console.log('✅ Schedule created:', schedule.custom_id)
    
    // 5. Check if tasks were created
    console.log('\n5. Checking if tasks were created...')
    
    // Wait a moment for task creation to complete
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const { data: createdTasks, error: tasksError } = await supabase
      .from('tasks')
      .select('id, title, status, assigned_to, project_id, shoot_id, due_date')
      .eq('shoot_id', schedule.id)
    
    if (tasksError) {
      console.error('Error fetching created tasks:', tasksError)
    } else {
      console.log(`Tasks created for schedule: ${createdTasks.length}`)
      createdTasks.forEach(task => {
        console.log(`  - ${task.title} (assigned: ${task.assigned_to ? 'Yes' : 'No'}, due: ${task.due_date})`)
      })
      
      if (createdTasks.length === 0) {
        console.log('❌ No tasks were created for the schedule')
        
        // Try to manually trigger task creation
        console.log('\n6. Manually testing task creation...')
        
        // Import the task creation function (this would need to be adapted for Node.js)
        console.log('Manual task creation would require importing the task creation functions')
        console.log('This should be tested through the API endpoint or frontend')
      } else {
        console.log('✅ Tasks were created successfully')
      }
    }
    
    // 7. Test direct task creation
    console.log('\n7. Testing direct task creation...')
    
    const taskData = {
      title: 'Test Manual Task',
      description: 'This is a manually created test task',
      status: 'pending',
      priority: 'medium',
      project_id: testProject.id,
      shoot_id: schedule.id,
      assigned_to: usersByRole.admin.length > 0 ? usersByRole.admin[0].id : 
                   usersByRole.pilot.length > 0 ? usersByRole.pilot[0].id : 
                   usersByRole.editor.length > 0 ? usersByRole.editor[0].id : null,
      due_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 3 days from now
    }
    
    if (!taskData.assigned_to) {
      console.log('❌ No users available to assign task to')
    } else {
      const { data: manualTask, error: manualTaskError } = await supabase
        .from('tasks')
        .insert(taskData)
        .select()
        .single()
      
      if (manualTaskError) {
        console.error('❌ Failed to create manual task:', manualTaskError.message)
      } else {
        console.log('✅ Manual task created successfully:', manualTask.title)
      }
    }
    
    // 8. Clean up test data
    console.log('\n8. Cleaning up test data...')
    
    // Delete tasks
    await supabase.from('tasks').delete().eq('shoot_id', schedule.id)
    await supabase.from('tasks').delete().eq('title', 'Test Manual Task')
    
    // Delete schedule
    await supabase.from('schedules').delete().eq('id', schedule.id)
    
    // Delete project if we created it
    if (testProject.name === 'Test Task Creation Project') {
      await supabase.from('projects').delete().eq('id', testProject.id)
    }
    
    // Delete client if we created it
    const testClient = clients.find(c => c.name === 'Test Task Creation Client')
    if (testClient) {
      await supabase.from('clients').delete().eq('id', testClient.id)
    }
    
    console.log('✅ Test data cleaned up')
    
    console.log('\n🎉 Task creation test completed!')
    console.log('\nSummary:')
    console.log('- User roles: Available ✅')
    console.log('- Schedule creation: Working ✅')
    console.log('- Automatic task creation: Check logs above')
    console.log('- Manual task creation: Check logs above')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testTaskCreation()
